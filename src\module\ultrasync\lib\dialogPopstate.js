
import DialogManager from './dialogManager'

export default {
    data(){
        return {
            currentDialogId: null
        }
    },
    watch: {
        dialogVisible: {
            handler(val) {
                this.dialogVisibleWatch && this.dialogVisibleWatch(val)

                if (val) {
                    // 弹窗打开时，注册到DialogManager
                    this.currentDialogId = DialogManager.register(this, {
                        open: this.showDialog.bind(this),
                        close: this.closeDialog.bind(this),
                        canCloseOnPopstate: this.checkCanCloseOnPopstate(),
                        canClose: this.checkCanClose()
                    });

                    // 标记为已打开
                    DialogManager.openDialog(this.currentDialogId);
                } else {
                    // 弹窗关闭时，从DialogManager注销
                    if (this.currentDialogId) {
                        DialogManager.unregister(this.currentDialogId);
                        this.currentDialogId = null;
                    }
                }
            }
        },
    },
    computed:{

    },
    methods:{
        checkCanCloseOnPopstate(){
            return true
        },
        closeDialog() {
            this.dialogVisible = false;
        },
        checkCanClose(){
            return true
        },
        checkIsShow(){
            return this.dialogVisible
        },
        showDialog(){
            this.dialogVisible = true;
        },
        /**
         * 更新弹窗属性
         * @param {Object} properties - 要更新的属性
         */
        updateDialogProperties(properties) {
            if (this.currentDialogId) {
                DialogManager.setDialogProperties(this.currentDialogId, properties);
            }
        },
        /**
         * 强制关闭弹窗（绕过canClose检查）
         */
        forceCloseDialog() {
            if (this.currentDialogId) {
                // 临时设置为可关闭
                this.updateDialogProperties({ canClose: true });
                this.closeDialog();
            }
        }
    },
    beforeDestroy() {
        // 组件销毁前清理DialogManager中的注册
        if (this.currentDialogId) {
            DialogManager.unregister(this.currentDialogId);
            this.currentDialogId = null;
        }
    }
}
