/**
 * DialogManager测试工具
 * 用于验证新的弹窗管理系统功能
 */

import DialogManager from './dialogManager'
import webViewBackHandler from './webviewBackHandler'
import Tool from '@/common/tool'

class DialogManagerTest {
    constructor() {
        this.testResults = [];
        this.mockDialogs = [];
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🧪 开始DialogManager测试...');
        
        this.testResults = [];
        
        // 基础功能测试
        await this.testBasicFunctionality();
        
        // 弹窗注册和注销测试
        await this.testDialogRegistration();
        
        // 路由拦截测试
        await this.testRouteInterception();
        
        // WebView返回处理测试
        await this.testWebViewBackHandler();
        
        // 平台兼容性测试
        await this.testPlatformCompatibility();
        
        // 错误处理测试
        await this.testErrorHandling();
        
        // 输出测试结果
        this.printTestResults();
        
        return this.testResults;
    }

    /**
     * 基础功能测试
     */
    async testBasicFunctionality() {
        console.log('📋 测试基础功能...');
        
        try {
            // 测试单例模式
            const instance1 = DialogManager;
            const instance2 = DialogManager;
            this.assert(instance1 === instance2, '单例模式测试');
            
            // 测试初始状态
            this.assert(!DialogManager.hasOpenDialogs(), '初始状态无弹窗');
            this.assert(DialogManager.getOpenDialogs().length === 0, '初始弹窗列表为空');
            
            // 测试状态获取
            const status = DialogManager.getStatus();
            this.assert(typeof status === 'object', '状态信息获取');
            this.assert(typeof status.totalDialogs === 'number', '状态信息包含弹窗总数');
            
        } catch (error) {
            this.fail('基础功能测试', error.message);
        }
    }

    /**
     * 弹窗注册和注销测试
     */
    async testDialogRegistration() {
        console.log('📋 测试弹窗注册和注销...');
        
        try {
            // 创建模拟弹窗
            const mockDialog = this.createMockDialog();
            
            // 测试注册
            const dialogId = DialogManager.register(mockDialog, {
                open: mockDialog.open,
                close: mockDialog.close,
                canCloseOnPopstate: true,
                canClose: true
            });
            
            this.assert(typeof dialogId === 'string', '弹窗注册返回ID');
            this.assert(DialogManager.getDialogById(dialogId) !== null, '注册后可以获取弹窗信息');
            
            // 测试打开
            const openResult = DialogManager.openDialog(dialogId);
            this.assert(openResult === true, '弹窗打开成功');
            this.assert(DialogManager.isDialogOpen(dialogId), '弹窗状态为打开');
            this.assert(DialogManager.hasOpenDialogs(), '系统检测到有打开的弹窗');
            
            // 测试关闭
            const closeResult = DialogManager.closeDialog(dialogId);
            this.assert(closeResult === true, '弹窗关闭成功');
            this.assert(!DialogManager.isDialogOpen(dialogId), '弹窗状态为关闭');
            
            // 测试注销
            const unregisterResult = DialogManager.unregister(dialogId);
            this.assert(unregisterResult === true, '弹窗注销成功');
            this.assert(DialogManager.getDialogById(dialogId) === null, '注销后无法获取弹窗信息');
            
        } catch (error) {
            this.fail('弹窗注册和注销测试', error.message);
        }
    }

    /**
     * 路由拦截测试
     */
    async testRouteInterception() {
        console.log('📋 测试路由拦截...');
        
        try {
            // 创建模拟弹窗
            const mockDialog = this.createMockDialog();
            const dialogId = DialogManager.register(mockDialog, {
                open: mockDialog.open,
                close: mockDialog.close,
                canCloseOnPopstate: true,
                canClose: true
            });
            
            // 打开弹窗
            DialogManager.openDialog(dialogId);
            
            // 模拟路由变化
            const mockTo = { path: '/test', matched: [{}] };
            const mockFrom = { path: '/home', matched: [{}] };
            let nextCalled = false;
            const mockNext = (result) => {
                nextCalled = true;
                return result;
            };
            
            // 测试路由拦截
            DialogManager.handleRouteChange(mockTo, mockFrom, mockNext);
            
            this.assert(nextCalled, '路由拦截调用了next函数');
            this.assert(!DialogManager.isDialogOpen(dialogId), '路由变化时弹窗被关闭');
            
            // 清理
            DialogManager.unregister(dialogId);
            
        } catch (error) {
            this.fail('路由拦截测试', error.message);
        }
    }

    /**
     * WebView返回处理测试
     */
    async testWebViewBackHandler() {
        console.log('📋 测试WebView返回处理...');
        
        try {
            // 测试WebView检测
            const status = webViewBackHandler.getStatus();
            this.assert(typeof status === 'object', 'WebView处理器状态获取');
            this.assert(typeof status.isWebView === 'boolean', 'WebView环境检测');
            
            // 测试初始化
            webViewBackHandler.init();
            const statusAfterInit = webViewBackHandler.getStatus();
            this.assert(statusAfterInit.isInitialized, 'WebView处理器初始化成功');
            
            // 如果在webview环境中，测试返回处理
            if (status.isWebView) {
                // 创建模拟弹窗
                const mockDialog = this.createMockDialog();
                const dialogId = DialogManager.register(mockDialog, {
                    open: mockDialog.open,
                    close: mockDialog.close,
                    canCloseOnPopstate: true,
                    canClose: true
                });
                
                DialogManager.openDialog(dialogId);
                
                // 模拟返回事件
                const mockEvent = new Event('backbutton');
                const handled = webViewBackHandler.handleBackButton(mockEvent);
                
                this.assert(handled === true, 'WebView返回事件被正确处理');
                this.assert(!DialogManager.isDialogOpen(dialogId), '返回手势关闭了弹窗');
                
                // 清理
                DialogManager.unregister(dialogId);
            }
            
        } catch (error) {
            this.fail('WebView返回处理测试', error.message);
        }
    }

    /**
     * 平台兼容性测试
     */
    async testPlatformCompatibility() {
        console.log('📋 测试平台兼容性...');
        
        try {
            // 测试平台检测
            const platforms = ['App', 'Android', 'IOS', 'PCBrowser', 'MobileBrowser'];
            const detectedPlatforms = platforms.filter(platform => Tool.checkAppClient(platform));
            
            this.assert(Array.isArray(detectedPlatforms), '平台检测返回数组');
            
            // 测试DialogManager在不同平台的兼容性
            const status = DialogManager.getStatus();
            this.assert(status.legacyMode === true, '兼容模式已启用');
            
            // 测试传统系统迁移
            const mockDialog = this.createMockDialog();
            const migratedId = DialogManager.migrateFromLegacy(mockDialog, 'test-legacy-id');
            
            this.assert(typeof migratedId === 'string', '传统弹窗迁移成功');
            this.assert(DialogManager.getDialogById(migratedId) !== null, '迁移后弹窗可访问');
            
            // 清理
            DialogManager.unregister(migratedId);
            
        } catch (error) {
            this.fail('平台兼容性测试', error.message);
        }
    }

    /**
     * 错误处理测试
     */
    async testErrorHandling() {
        console.log('📋 测试错误处理...');
        
        try {
            // 测试无效ID操作
            this.assert(DialogManager.openDialog('invalid-id') === false, '无效ID打开弹窗返回false');
            this.assert(DialogManager.closeDialog('invalid-id') === false, '无效ID关闭弹窗返回false');
            this.assert(DialogManager.unregister('invalid-id') === false, '无效ID注销返回false');
            this.assert(DialogManager.getDialogById('invalid-id') === null, '无效ID获取弹窗返回null');
            
            // 测试空参数
            try {
                DialogManager.register(null, {});
                this.fail('空参数注册测试', '应该抛出错误');
            } catch (e) {
                this.pass('空参数注册正确抛出错误');
            }
            
        } catch (error) {
            this.fail('错误处理测试', error.message);
        }
    }

    /**
     * 创建模拟弹窗
     */
    createMockDialog() {
        const mockDialog = {
            isOpen: false,
            open() {
                this.isOpen = true;
            },
            close() {
                this.isOpen = false;
            },
            showDialog() {
                this.open();
            },
            closeDialog() {
                this.close();
            },
            checkCanClose() {
                return true;
            },
            checkCanCloseOnPopstate() {
                return true;
            }
        };
        
        this.mockDialogs.push(mockDialog);
        return mockDialog;
    }

    /**
     * 断言测试
     */
    assert(condition, testName) {
        if (condition) {
            this.pass(testName);
        } else {
            this.fail(testName, '断言失败');
        }
    }

    /**
     * 记录测试通过
     */
    pass(testName) {
        this.testResults.push({
            name: testName,
            status: 'PASS',
            message: '✅ 通过'
        });
    }

    /**
     * 记录测试失败
     */
    fail(testName, message) {
        this.testResults.push({
            name: testName,
            status: 'FAIL',
            message: `❌ 失败: ${message}`
        });
    }

    /**
     * 打印测试结果
     */
    printTestResults() {
        console.log('\n📊 测试结果汇总:');
        console.log('='.repeat(50));
        
        let passCount = 0;
        let failCount = 0;
        
        this.testResults.forEach(result => {
            console.log(`${result.message} ${result.name}`);
            if (result.status === 'PASS') {
                passCount++;
            } else {
                failCount++;
            }
        });
        
        console.log('='.repeat(50));
        console.log(`总计: ${this.testResults.length} 个测试`);
        console.log(`通过: ${passCount} 个`);
        console.log(`失败: ${failCount} 个`);
        console.log(`成功率: ${((passCount / this.testResults.length) * 100).toFixed(1)}%`);
        
        if (failCount === 0) {
            console.log('🎉 所有测试通过！');
        } else {
            console.log('⚠️  有测试失败，请检查实现');
        }
    }

    /**
     * 清理测试环境
     */
    cleanup() {
        // 清理所有模拟弹窗
        this.mockDialogs.forEach(dialog => {
            if (dialog.isOpen) {
                dialog.close();
            }
        });
        this.mockDialogs = [];
        
        // 清理DialogManager中的所有弹窗
        DialogManager.closeAllDialogs();
        
        console.log('🧹 测试环境清理完成');
    }
}

// 导出测试类
export default DialogManagerTest;

// 如果在浏览器环境中，添加到全局对象以便调试
if (typeof window !== 'undefined') {
    window.DialogManagerTest = DialogManagerTest;
}
