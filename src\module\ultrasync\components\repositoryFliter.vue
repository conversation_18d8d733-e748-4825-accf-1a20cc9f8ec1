<template>
    <div>
        <van-popup
            v-model="isShow"
            position="bottom"
            :round="true"
            :closeable="true"
            close-icon-position="top-left"
            @click-close-icon="handeIconBack()"
            @click-overlay="handeIconBack()"
            :duration="animateDuration"
            close-icon="icon iconfont icon-icon- repository_filter_close_icon"
            :style="{ width: '100%', height: '40%', overflow: 'hidden', 'border-radius': 0 }"
        >
            <div class="repository_fliter">
                <div class="header">
                    {{ lang.search_text }}
                </div>
                <div class="content">
                    <!-- <div class='row' v-for="(list,field_name) of {category:newCondition.category}" :key="field_name">
                        <div class='title_first file_font'>{{lang.library[field_name]? lang.library[field_name] : lang[field_name]}}</div>
                        <template v-if="isArrary(list)">
                            <div class='fields' >
                                <div class="fl"  :class="{'field':true,'active': filed.is_checked}" v-for="(filed) of list" :key="filed.id" @click="filedChange(filed,field_name)">
                                    <div :class="{'active__': filed.is_checked}">
                                        {{filed.name}}
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div> -->
                    <div class="row" v-for="(list, field_name) of { tag: newCondition.tag }" :key="field_name">
                        <div class="title_first file_font">
                            {{ lang.library[field_name] ? lang.library[field_name] : lang[field_name] }}
                        </div>
                        <template v-if="isArrary(list)">
                            <div class="fields">
                                <div
                                    class="fl"
                                    :class="{ field: true, active: filed.is_checked }"
                                    v-for="filed of list.filter((v) => v.in_pop)"
                                    :key="filed.id"
                                    @click="filedChange(filed, field_name)"
                                >
                                    <div :class="{ active__: filed.is_checked }">
                                        {{ getTagName(filed) }}
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <div class="button_arear">
                    <div class="file_font cancel" @click="handleReset()">
                        <div>{{ lang.register_reset_btn }}</div>
                    </div>
                    <div class="file_font confirm" @click="handleConfirm()">
                        <template v-if="is_set_condition">
                            <div>
                                {{
                                    lang.check_filter_result_of_post.replace(
                                        "${count}",
                                        newTotal && newTotal > 99 ? "99+" : newTotal || 0
                                    )
                                }}
                            </div>
                        </template>
                        <template v-else>
                            <div>{{ lang.confirm_txt }}</div>
                        </template>
                    </div>
                </div>
            </div>
        </van-popup>
    </div>
</template>
<script>
import libraryService from "../service/libraryService.js";
import base from "../lib/base";
import { Toast } from "vant";
import Tool from "@/common/tool.js";
import { cloneDeep, includes } from "lodash";
import dialogPopstate from "../lib/dialogPopstate";
import { ActionSheet, Popup } from "vant";
export default {
    mixins: [base, dialogPopstate],
    name: "repositoryFliter",
    components: { VanPopup: Popup },

    props: {
        filters: {
            type: Object,
            default: () => {
                return {};
            },
        },
        isShowCondition: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            isSearchLoading: false,
            isShow: false,
            newTotal: 0,
            is_changed: false,
            oldCondition: {},
            newCondition: {},
            defaultCondition: {
                tag: [],
                // category:[],
                tag_id: [],
                // category_id:[]
            },
            //lang.case_database_fliter['case_database_fliter']
            allCondition: {},
            animateDuration: 0.3,
        };
    },
    computed: {
        dialogVisible: {
            get() {
                if (this.isShow) {
                    this.oldCondition = cloneDeep(this.defaultCondition);
                    this.newCondition = cloneDeep(this.defaultCondition);
                }
                this.sliderKey = 0;
                return this.isShow;
            },
            set(val) {
                this.$emit("update:isShowCondition", val);
            },
        },
        is_set_condition() {
            for (let key in this.newCondition) {
                let list = this.newCondition[key];
                for (let gkey in this.newCondition[key]) {
                    if (this.newCondition[key][gkey].is_checked) {
                        return true;
                    }
                }
            }
            return false;
        },
    },
    created() {
        this.oldCondition = cloneDeep(this.defaultCondition);
        this.newCondition = cloneDeep(this.defaultCondition);
    },
    mounted() {},
    beforeDestroy() {},
    watch: {
        isShowCondition(v, ov) {
            this.isShow = v;
        },
        filters: {
            handler(newVal) {
                this.newCondition = cloneDeep(newVal.condition);
                this.newTotal = this.$store.state.libraryData.total || 0;
                this.oldCondition = cloneDeep(newVal.condition);
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        getTagName(item) {
            let name = item.name;
            if (window.vm.$store.state.language.currentLanguage == "CN") {
                name = item.name;
            } else {
                name = item.meta && item.meta.tag_en ? item.meta.tag_en[0] : item.name;
            }
            return name||item.name;
        },
        checkChanged() {
            this.is_changed = JSON.stringify(this.oldCondition) != JSON.stringify(this.newCondition);
        },
        async getPostsCountByCondition() {
            let that = this;
            this.isSearchLoading = true;
            return new Promise((resolve, reject) => {
                let timer = setTimeout(() => {
                    Toast(that.lang.requestTimeout);
                    that.isSearchLoading = false;
                    resolve({ data: [], total: 0 });
                }, 30 * 1000);
                let params = { per_page: 1, page: 1, request_type: "post" };
                let tags = this.newCondition.tag.reduce((h, v) => {
                    if (v.is_checked) {
                        h.push(v.id);
                    }
                    return h;
                }, []);
                // let categories = this.newCondition.category.reduce((h,v)=>{
                //     if(v.is_checked){
                //         h.push(v.id)
                //     }
                //     return h
                // },[])
                // if(categories.length>0){
                //     params.categories = categories.join(',')
                // }

                if (tags.length > 0) {
                    params["tag_ids"] = tags; //.join(",");
                }
                if (this.filters.searchInputKey) {
                    params.search = this.filters.searchInputKey;
                }
                libraryService.searchPosts(params).then(async (res) => {
                    // console.error('getPosts:',res)
                    clearTimeout(timer);
                    if (res.status == 200) {
                        this.newTotal = res.data["total"] || 0;
                        // this.$store.commit('libraryData/updateLibraryData',{posts:res.data||[],total:this.newTotal})
                        // resolve({data:res.data,total:res.headers['x-wp-total'] || 0})
                    } else {
                        Toast(that.lang.operate_err);
                        resolve({ data: [], total: 0 });
                    }
                    this.isSearchLoading = false;
                });
            });
        },
        handeIconBack() {
            this.newCondition = cloneDeep(this.oldCondition);
            this.handleback();
        },
        handleReset() {
            for (let key in this.newCondition) {
                let list = this.newCondition[key];
                this.defaultCondition[key] = this.newCondition[key].reduce((h, v) => {
                    v.is_checked = false;
                    h.push(v);
                    return h;
                }, []);
            }
            this.newCondition = cloneDeep(this.defaultCondition);
            this.checkChanged();
            this.oldCondition = cloneDeep(this.defaultCondition);
            this.handleback();
        },
        handleConfirm() {
            this.oldCondition = cloneDeep(this.newCondition);
            // let newExams =  this.fliterExams()
            // this.$parent.handleback(this.newCondition)
            this.handleback();
        },
        isArrary(a) {
            return a instanceof Array;
        },
        handleback() {
            this.dialogVisible = false;

            this.newCondition = cloneDeep(this.oldCondition);
            if (!this.is_changed) {
                return;
            }
            this.$parent.handleback(this.newCondition, true);
            this.checkChanged();
        },
        async filedChange(field, parent, gparent) {
            var condition = cloneDeep(this.newCondition);
            if (gparent) {
                condition[gparent] = condition[gparent] || {};
                condition[gparent][parent] = condition[gparent][parent] || [];
                condition[gparent][parent] = this.newCondition[gparent][parent].reduce((h, v) => {
                    if (v.id == field.id) {
                        v.is_checked = !v.is_checked;
                    }
                    h.push(v);
                    return h;
                }, []);
            } else {
                condition[parent] = condition[parent] || [];
                condition[parent] = this.newCondition[parent].reduce((h, v) => {
                    if (v.id == field.id) {
                        v.is_checked = !v.is_checked;
                    }
                    h.push(v);
                    return h;
                }, []);
            }
            this.newCondition = condition;
            this.checkChanged();
            let new_post = await this.getPostsCountByCondition();
            // this.handleback()
        },
    },
};
</script>
<style lang="scss" scoped>
.repository_fliter {
    position: relative;
    height: 100%;
    .repository_filter_close_icon {
        color: red;
        font-size: 0.7rem;
        left: 0.5rem;
        top: 0.85rem;
        font-size: 1rem;
    }
    .header {
        text-align: center;
        line-height: 2.35rem;
        height: 2.35rem;
        font-size: 0.9rem;
        border-bottom: 0.1rem solid rgb(153, 153, 153, 0.2);
    }
    .file_font {
        font-family: "Arial";
        font-style: normal;
        color: #333;
        // text-overflow: ellipsis;
        // overflow: hidden;
        // white-space: nowrap;
    }
    .content {
        overflow: auto;
        padding: 0rem 0.9rem 0.5rem 0.6rem;
        height: calc(100% - 5.35rem);
        display: flex;
        flex-direction: column;
        .title_first {
            padding: 0.4rem 0rem 0.2rem 0rem;
            font-weight: 550;
            font-size: 0.75rem;
        }
        .title_second {
            padding: 0.3rem 0rem 0.3rem 0.2rem;
            font-size: 0.7rem;
        }
        .fields {
            padding: 0.2rem 0rem 0.2rem 0.2rem;
            // display:flex;
            flex-direction: row;
            width: 100%;
            flex-wrap: wrap;

            // justify-content: space-between;
            align-items: center;
            align-content: flex-end;
            grid-template-columns: repeat(auto-fill, 45px);
            grid-gap: 5px 15px;
        }
        .field {
            margin-bottom: 0.35rem;
            margin-right: 0.35rem;
            border-radius: 0.2rem;
            // width:27% !important;
            text-align: center;
            height: auto;
            line-height: normal;
            font-size: 0.6rem;
            color: #666;
            background-color: #f7f7f7;
            display: block;
            padding: 0.3rem 0.2rem;

            max-width: calc(100% - 1.3rem);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            div {
                overflow: hidden;
                text-overflow: inherit;
                white-space: nowrap;
                max-width: 100%;
            }
        }
        // .field:last-child:nth-child(3n - 1) {
        //     margin-right: calc(37.5% - 0.1rem);
        // }
        .active {
            color: white;
            background-color: #00c59d;
        }
        .active_ {
            position: relative;
        }
        .active_:after {
            content: "";
            position: absolute;
            right: -0.1rem;
            bottom: -0.25rem;
            width: 0.65rem;
            height: 0.65rem;
            border-radius: 0 0 0.1rem 0;
            background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAJFJREFUOE/lkjEOAWEUhL/vAlpxBWqFhEso3EDtChqlRuUMGpdA7wBqxxjZhOQXdhUb1b7uJTPfe5mMtBxb+ukKIMkYWAEndV/m9jWDJAP1XgmTLIAd0K929c3zAUhyASbAEugB2+LiVD03flAASt0NmL2++gUYAgdg9BQe1XldX+oyqCAb4Kqum8rWlSL9NYMH5egjEdmYjQIAAAAASUVORK5CYII=)
                no-repeat;
            background-size: 1rem auto;
            overflow: hidden;
            padding-bottom: 0.18rem;
            padding-right: 0.18rem;
            opacity: 0.6;
            z-index: 1;
        }
        .row {
            width: 100%;
        }
    }
    .button_arear {
        padding-top: 0.25rem;
        display: flex;
        border-top: 0.05rem solid rgb(153, 153, 153, 0.2);
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: space-between;
        align-items: center;
        height: 2rem;
        font-size: 0.85rem;
        .cancel {
            display: flex;
            align-items: center;
            text-align: center;
            height: 100%;
            width: 50%;
            background-color: #62e1c7;
            text-align: center;
            margin-left: 1rem;
            border-top-left-radius: 1.5rem;
            border-bottom-left-radius: 1.5rem;
            div {
                width: 100%;
                text-align: center;
            }
        }
        .confirm {
            display: flex;
            align-items: center;
            height: 100%;
            width: 50%;
            text-align: center;
            background-color: #00c59d;
            color: white;
            margin-right: 1rem;
            border-top-right-radius: 1.5rem;
            border-bottom-right-radius: 1.5rem;
            div {
                width: 100%;
                text-align: center;
            }
        }
    }
}
</style>
<style>
.repository_filter_close_icon {
    color: #464646;
    left: 0.5rem;
    top: 0.7rem;
}
</style>
