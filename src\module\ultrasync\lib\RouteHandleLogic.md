# 路由处理逻辑说明

## 概述

`DialogManager.handleRouteChange()` 方法处理路由变化时的弹窗逻辑，确保在webview环境中原生返回手势能正确处理弹窗。

## 核心逻辑

### 1. 无弹窗情况
```
如果没有打开的弹窗 → 直接允许路由通过 (next())
```

### 2. 有弹窗情况
只关注**最后一个（最顶层）弹窗**的状态：

#### 2.1 最后弹窗不可通过popstate关闭
```
canCloseOnPopstate = false → 阻止路由返回 (next(false))，不关闭弹窗
```

#### 2.2 最后弹窗可以通过popstate关闭
```
canCloseOnPopstate = true → 关闭最后弹窗，但阻止路由返回 (next(false))
```

## 设计原理

### 为什么只处理最后一个弹窗？
- 弹窗通常是栈式管理，最后打开的在最顶层
- 用户的返回操作应该只影响当前可见的（最顶层）弹窗
- 避免一次性关闭所有弹窗的突兀体验

### 为什么关闭弹窗后仍然阻止路由？
- 用户的返回意图是关闭弹窗，而不是真正的页面返回
- 关闭弹窗后立即返回页面会让用户感到困惑
- 如果用户真的想返回页面，可以再次执行返回操作

### 为什么不可关闭弹窗要阻止路由？
- 某些弹窗（如正在加载数据）不应该被返回手势打断
- 保护重要的用户操作流程不被意外中断

## 使用场景

### 场景1：数据加载弹窗
```javascript
// 数据加载时不允许返回关闭
const dialogId = DialogManager.register(component, {
    canCloseOnPopstate: false,  // 加载时不允许返回关闭
    canClose: false
});
```

### 场景2：普通信息弹窗
```javascript
// 普通弹窗允许返回关闭
const dialogId = DialogManager.register(component, {
    canCloseOnPopstate: true,   // 允许返回关闭
    canClose: true
});
```

### 场景3：多层弹窗
```javascript
// 打开多个弹窗
DialogManager.openDialog(dialogId1);  // 底层弹窗
DialogManager.openDialog(dialogId2);  // 顶层弹窗

// 返回操作只会处理 dialogId2（最后打开的）
```

## 测试验证

运行测试来验证逻辑：
```javascript
import RouteHandleTest from './routeHandleTest'
new RouteHandleTest().runTests()
```

## 调试信息

方法会输出调试信息：
- `"DialogManager: 阻止路由返回，弹窗不允许popstate关闭: {dialogId}"`
- `"DialogManager: 关闭最后一个弹窗，阻止路由返回: {dialogId}"`

## 与webview集成

这个逻辑与 `WebViewBackHandler` 配合工作：
1. `WebViewBackHandler` 检测到返回操作
2. 调用 `DialogManager.handleRouteChange()`
3. 根据弹窗状态决定是否关闭弹窗和是否允许路由

## 注意事项

1. **弹窗顺序**：确保弹窗按正确顺序打开，最重要的弹窗最后打开
2. **状态管理**：及时更新弹窗的 `canCloseOnPopstate` 属性
3. **用户体验**：合理设置哪些弹窗可以被返回关闭
4. **调试**：关注控制台输出，了解路由处理的决策过程

## 流程图

```
用户执行返回操作
        ↓
检查是否有打开的弹窗
        ↓
    无弹窗 → 允许路由通过
        ↓
    有弹窗 → 获取最后一个弹窗
        ↓
检查最后弹窗的canCloseOnPopstate
        ↓
    false → 阻止路由，不关闭弹窗
        ↓
    true → 关闭弹窗，阻止路由
```

这种设计确保了在webview环境中，用户的返回手势能够按预期工作，优先处理弹窗而不是页面返回。
