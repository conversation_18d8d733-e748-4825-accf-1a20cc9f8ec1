/**
 * WebView返回处理器
 * 专门处理webview环境下的原生返回手势/按钮
 * 确保弹窗优先于路由返回被处理
 */

import Tool from '@/common/tool'
import DialogManager from './dialogManager'

class WebViewBackHandler {
    constructor() {
        this.isInitialized = false;
        this.isWebView = this.detectWebView();
        this.handlers = new Map();
        this.isHandling = false;

        // 绑定方法到实例
        this.handleBackButton = this.handleBackButton.bind(this);
        this.handlePopState = this.handlePopState.bind(this);
        this.handleKeyDown = this.handleKeyDown.bind(this);
    }

    /**
     * 检测是否在webview环境中
     * @returns {boolean}
     */
    detectWebView() {
        const ua = navigator.userAgent;

        // 检查各种webview标识
        const webviewIndicators = [
            'wv',
            'WebView',
            'Android.*wv',
            'iPhone.*Mobile.*Safari.*Version',
            'iPad.*Mobile.*Safari.*Version'
        ];

        const hasWebViewUA = webviewIndicators.some(indicator =>
            new RegExp(indicator, 'i').test(ua)
        );

        // 检查平台特定的webview环境
        const isAppClient = Tool.checkAppClient('App') ||
                           Tool.checkAppClient('Android') ||
                           Tool.checkAppClient('IOS');

        // 检查webkit消息处理器（iOS webview）
        const hasWebKit = !!(window.webkit && window.webkit.messageHandlers);

        // 检查是否在iframe中（某些webview实现）
        const isInFrame = window.self !== window.top;

        return hasWebViewUA || isAppClient || hasWebKit || isInFrame;
    }

    /**
     * 初始化返回处理器
     */
    init() {
        if (this.isInitialized || !this.isWebView) {
            return;
        }

        console.log('WebViewBackHandler: Initializing for webview environment');

        // Android webview返回按钮处理
        if (Tool.checkAppClient('Android')) {
            this.initAndroidBackHandler();
        }

        // iOS webview手势返回处理
        if (Tool.checkAppClient('IOS')) {
            this.initIOSBackHandler();
        }

        // 通用popstate处理（适用于所有webview）
        this.initPopStateHandler();

        // 键盘返回键处理（适用于某些webview）
        this.initKeyboardHandler();

        this.isInitialized = true;
        console.log('WebViewBackHandler: Initialization complete');
    }

    /**
     * 初始化Android返回按钮处理
     */
    initAndroidBackHandler() {
        // Android webview通常会触发backbutton事件
        const backButtonHandler = (event) => {
            if (this.handleBackButton(event)) {
                event.preventDefault();
                event.stopPropagation();
                return false;
            }
        };

        document.addEventListener('backbutton', backButtonHandler, true);
        this.handlers.set('backbutton', backButtonHandler);

        // 某些Android webview可能使用keydown事件
        const keyDownHandler = (event) => {
            if (event.keyCode === 4) { // Android back key
                if (this.handleBackButton(event)) {
                    event.preventDefault();
                    event.stopPropagation();
                    return false;
                }
            }
        };

        document.addEventListener('keydown', keyDownHandler, true);
        this.handlers.set('keydown', keyDownHandler);
    }

    /**
     * 初始化iOS返回手势处理
     */
    initIOSBackHandler() {
        // iOS主要通过popstate事件处理
        // 在initPopStateHandler中统一处理

        // 监听触摸事件，检测边缘滑动手势
        let touchStartX = 0;
        const touchStartHandler = (event) => {
            touchStartX = event.touches[0].clientX;
        };

        const touchEndHandler = (event) => {
            const touchEndX = event.changedTouches[0].clientX;
            const deltaX = touchEndX - touchStartX;

            // 检测从左边缘向右滑动的手势
            if (touchStartX < 20 && deltaX > 50) {
                if (this.handleBackButton(event)) {
                    event.preventDefault();
                    event.stopPropagation();
                }
            }
        };

        document.addEventListener('touchstart', touchStartHandler, { passive: false });
        document.addEventListener('touchend', touchEndHandler, { passive: false });

        this.handlers.set('touchstart', touchStartHandler);
        this.handlers.set('touchend', touchEndHandler);
    }

    /**
     * 初始化popstate事件处理
     */
    initPopStateHandler() {
        const popStateHandler = (event) => {
            if (this.handlePopState(event)) {
                event.preventDefault();
                event.stopPropagation();

                // 阻止浏览器默认的返回行为
                history.pushState(null, null, location.href);
            }
        };

        window.addEventListener('popstate', popStateHandler, true);
        this.handlers.set('popstate', popStateHandler);

        // 添加一个历史记录条目，确保popstate能被触发
        history.pushState(null, null, location.href);
    }

    /**
     * 初始化键盘处理
     */
    initKeyboardHandler() {
        const keyDownHandler = (event) => {
            this.handleKeyDown(event);
        };

        document.addEventListener('keydown', keyDownHandler, true);
        this.handlers.set('keyboard', keyDownHandler);
    }

    /**
     * 处理返回按钮/手势
     * @param {Event} event
     * @returns {boolean} 是否已处理（阻止默认行为）
     */
    handleBackButton(event) {
        if (this.isHandling) {
            return true; // 防止重复处理
        }

        this.isHandling = true;

        try {
            // 检查是否有打开的弹窗
            if (DialogManager.hasOpenDialogs()) {
                const lastDialog = DialogManager.getLastOpenDialog();

                if (lastDialog) {
                    const [dialogId, dialogInfo] = lastDialog;

                    // 检查是否可以通过返回手势关闭
                    if (dialogInfo.canCloseOnPopstate && dialogInfo.canClose) {
                        DialogManager.closeDialog(dialogId);

                        console.log('WebViewBackHandler: Closed dialog via back gesture:', dialogId);
                        return true; // 已处理，阻止默认行为
                    } else {
                        console.log('WebViewBackHandler: Dialog cannot be closed via back gesture:', dialogId);
                        return true; // 仍然阻止默认行为，但不关闭弹窗
                    }
                }
            }

            // 没有弹窗需要处理，允许默认行为
            return false;
        } finally {
            // 延迟重置处理标志，避免快速连续触发
            setTimeout(() => {
                this.isHandling = false;
            }, 100);
        }
    }

    /**
     * 处理popstate事件
     * @param {PopStateEvent} event
     * @returns {boolean} 是否已处理
     */
    handlePopState(event) {
        return this.handleBackButton(event);
    }

    /**
     * 处理键盘事件
     * @param {KeyboardEvent} event
     */
    handleKeyDown(event) {
        // ESC键也可以用来关闭弹窗
        if (event.keyCode === 27) { // ESC key
            if (DialogManager.hasOpenDialogs()) {
                const lastDialog = DialogManager.getLastOpenDialog();
                if (lastDialog) {
                    const [dialogId, dialogInfo] = lastDialog;
                    if (dialogInfo.canClose) {
                        DialogManager.closeDialog(dialogId);
                        event.preventDefault();
                        event.stopPropagation();
                    }
                }
            }
        }
    }

    /**
     * 销毁处理器，清理事件监听器
     */
    destroy() {
        if (!this.isInitialized) {
            return;
        }

        // 清理所有事件监听器
        for (const [eventType, handler] of this.handlers) {
            switch (eventType) {
            case 'backbutton':
            case 'keydown':
            case 'keyboard':
                document.removeEventListener(eventType === 'keyboard' ? 'keydown' : eventType, handler, true);
                break;
            case 'popstate':
                window.removeEventListener('popstate', handler, true);
                break;
            case 'touchstart':
            case 'touchend':
                document.removeEventListener(eventType, handler);
                break;
            }
        }

        this.handlers.clear();
        this.isInitialized = false;
        this.isHandling = false;

        console.log('WebViewBackHandler: Destroyed');
    }

    /**
     * 获取处理器状态
     * @returns {Object}
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            isWebView: this.isWebView,
            isHandling: this.isHandling,
            handlersCount: this.handlers.size,
            platform: {
                android: Tool.checkAppClient('Android'),
                ios: Tool.checkAppClient('IOS'),
                app: Tool.checkAppClient('App')
            }
        };
    }
}

// 创建单例实例
const webViewBackHandler = new WebViewBackHandler();

// 自动初始化
if (typeof window !== 'undefined') {
    // 等待DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            webViewBackHandler.init();
        });
    } else {
        webViewBackHandler.init();
    }
}

export default webViewBackHandler;
