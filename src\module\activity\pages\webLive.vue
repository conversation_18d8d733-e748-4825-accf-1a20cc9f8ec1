<template>
    <div v-if="clientEnv.mobile">
        <div class="weixin-tip" v-if="!isSupport">
            <div class="tip-content">
                <h3 class="cover_tip_text">{{ lang.no_support_browser_live }}</h3>
            </div>
        </div>
        <!-- 恢复头部导航，在横屏时隐藏 -->
        <div class="mobile-header" :class="{ 'hidden-landscape': isScreenHorizontal }">
            <div class="header-content">
                <div class="header-title">{{ title || lang.live_room }}</div>
            </div>
        </div>

        <div class="mobile-container" :class="{ 'landscape-mode': isScreenHorizontal }">
            <!-- 精简的状态栏 -->
            <div class="ios-status-card" :class="{ 'hidden-landscape': isScreenHorizontal }" v-if="live_id">
                <div class="status-indicator">
                    <span class="status-dot" :class="getStatusClass()"></span>
                    <span class="status-text">{{ getStatusText() }}</span>
                </div>
                <div class="live-time" v-if="start_ts && end_ts">
                    {{ formatTime(start_ts) }} - {{ formatTime(end_ts) }}
                </div>
            </div>

            <!-- 临时直播状态卡片 -->
            <div class="ios-status-card" :class="{ 'hidden-landscape': isScreenHorizontal }" v-else>
                <div class="live-subject" v-if="livingStatus === 1">
                    {{ `${hostInfo.nickname}${lang.initiated_live_broadcast}` }}
                </div>
                <div class="status-indicator">
                    <span class="status-dot" :class="getTempStatusClass()"></span>
                    <span class="status-text">{{ getTempStatusText() }}</span>
                </div>
            </div>

            <!-- 用户信息卡片 -->
            <div class="ios-info-card" :class="{ 'hidden-landscape': isScreenHorizontal }">
                <div class="info-row">
                    <span class="info-label">{{ lang.user_name }}</span>
                    <div class="info-value">
                        {{ myNickName }}
                        <van-icon name="edit" @click.native.stop="changeNickName" color='#007AFF' size='18px'/>
                    </div>
                </div>
                <div class="info-row" v-if="live_id && title">
                    <span class="info-label">{{ lang.live_theme }}</span>
                    <span class="info-value">{{ title }}</span>
                </div>
                <div class="info-row" v-if="live_id && dec">
                    <span class="info-label">{{ lang.live_des }}</span>
                    <span class="info-value">{{ dec }}</span>
                </div>
            </div>

            <!-- 视频播放器容器 -->
            <div
                ref="fullscreenElement"
                class="ios-player-container"
                :class="[isFullScreenByManual ? 'fullscreen' : '', isScreenHorizontal ? 'horizontal' : '']"
            >
                <div class="player-box">
                    <div id="player-box-1" @dblclick="toggleBrowserFullscreen">
                        <custom-player
                            :isOnline="currentSubscribedMain.video"
                            :offlineTips="lang.weblive_live_not_yet"
                            :showControl="false"
                        >
                            <template>
                                <div id="remoteMainPlayer"></div>
                            </template>
                        </custom-player>
                    </div>
                    <div id="player-box-2" v-draggable @tap="swapVideoContent" v-show="checkShowSmallPlayerBox" class="aux-video">
                        <custom-player
                            :isOnline="currentSubscribedAux.video"
                            :hasMuteBtn="false"
                            :offlineTips="lang.weblive_live_not_yet"
                            :showControl="false"
                        >
                            <template>
                                <div id="remoteAuxPlayer"></div>
                            </template>
                        </custom-player>
                        <div class="swap-indicator">
                            <van-icon name="exchange" size="16px" color="white" />
                        </div>
                    </div>
                </div>

                <!-- iOS风格的视频控制栏 -->
                <div class="ios-controls" :class="{ 'landscape-controls': isScreenHorizontal }">
                    <div class="controls-left">
                        <div class="control-btn mute-btn" @click="toggleMute" :class="{ 'muted': currentMute }">
                            <van-icon v-show="currentMute" name="volume-o" size="20px" color="white" />
                            <van-icon v-show="!currentMute" name="volume" size="20px" color="white" />
                        </div>
                    </div>
                    <div class="controls-right">
                        <div class="control-btn fullscreen-btn" v-if="!isScreenHorizontal" @click="toggleFullscreen">
                            <van-icon v-if="!isFullScreenByManual" name="enlarge" size="20px" color="white" />
                            <van-icon v-else name="shrink" size="20px" color="white" />
                        </div>
                    </div>
                </div>

                <!-- 刷新按钮 -->
                <div class="refresh-container" v-if="livingStatus === 2">
                    <van-button class="ios-refresh-btn" type="primary" @click="refreshStatus">
                        {{ lang.refresh_status }}
                    </van-button>
                </div>
            </div>


            <!-- 下载提示 -->
            <div class="ios-download-tip" :class="{ 'hidden-landscape': isScreenHorizontal }">
                <a href="javascript:void(0)" @click="gotoDownLoadApp" class="download-link">
                    <van-icon name="down" size="14px" color="white" />
                    <span>{{ lang.weblive_download_app_tips }}</span>
                </a>
            </div>
        </div>

        <!-- 用户名输入对话框 -->
        <CommonDialog
            v-model="showNewNameDialog"
            :title="lang.input_your_name"
            :showRejectButton="false"
            :showCancelButton="false"
            :forbiddenClose="true"
            :beforeClose="handleCloseNewNameDialog"
        >
            <van-form ref="newNameForm" validate-trigger="onSubmit" label-width="0">
                <van-field
                    v-model.trim="newNameForm.name"
                    :placeholder="`${lang.input_your_name}`"
                    maxlength="100"
                    name="name"
                    :rules="[{ validator: validatorRenameValue, message: `${lang.web_live_name_support_tips}` }]"
                    @blur.stop
                />
            </van-form>
        </CommonDialog>
    </div>
    <div v-else class="pc-layout">
        <div class="weixin-tip" v-if="!isSupport">
            <div class="tip-content">
                <h3 class="cover_tip_text">{{ lang.no_support_browser_live }}</h3>
            </div>
        </div>
        <el-header class="el-pc-header">
            <div class="el-pc-header-content">
                <div class="el-pc-header-title">{{ title || lang.live_room }}</div>
                <div class="el-pc-header-right">
                    <van-button type="primary" size="normal" @click="toggleBrowserFullscreen">
                        <van-icon name="enlarge" size="16px" color="white" />
                    </van-button>
                </div>
            </div>
        </el-header>
        <div class="pc-container">
            <div class="pc-left-container">
                <div
                    ref="pcFullscreenElement"
                    class="pc-player-container"
                    :class="[isFullScreenByManual ? 'fullscreen' : '', isScreenHorizontal ? 'horizontal' : '']"
                >
                    <div class="pc-player-box" :class="{'has-aux-video': checkShowSmallPlayerBox}">
                        <div id="player-box-1" @dblclick="toggleBrowserFullscreen">
                            <custom-player
                                :isOnline="currentSubscribedMain.video"
                                :offlineTips="lang.weblive_live_not_yet"
                                :showControl="false"
                            >
                                <template>
                                    <div id="remoteMainPlayer"></div>
                                </template>
                            </custom-player>
                        </div>
                        <div id="player-box-2" @click="swapVideoContent" v-show="checkShowSmallPlayerBox" class="aux-video">
                            <custom-player
                                :isOnline="currentSubscribedAux.video"
                                :hasMuteBtn="false"
                                :offlineTips="lang.weblive_live_not_yet"
                                :showControl="false"
                            >
                                <template>
                                    <div id="remoteAuxPlayer"></div>
                                </template>
                            </custom-player>
                            <div class="swap-hint">
                                <van-icon name="exchange" size="24px" color="white" />
                                <span>{{lang.switch_video}}</span>
                            </div>
                        </div>
                    </div>
                    <div class="pc-player-controls">
                        <div class="pc-controls-left"></div>
                        <div class="pc-controls-right">
                            <div @click="toggleMute" class="pc-control-btn pc-mute-btn" :class="{ 'muted': currentMute }">
                                <van-icon v-show="currentMute" name="volume-o" size="24px" color="white" />
                                <van-icon v-show="!currentMute" name="volume" size="24px" color="white" />
                            </div>
                        </div>
                    </div>
                    <van-button plain class="refresh_button" type="info" @click="refreshStatus" v-if="livingStatus === 2">
                        {{ lang.refresh_status }}
                    </van-button>
                </div>
            </div>
            <div class="info-sidebar-trigger"></div>
            <div class="pc-right-container">
                <div class="pc-info-card">
                    <div class="pc-status-content" v-if="live_id">
                        <div class="pc-date" v-if="start_ts && end_ts">{{ formatTime(start_ts) }} - {{ formatTime(end_ts) }}</div>
                    </div>
                    <div class="pc-status-indicator" v-if="live_id">
                        <span class="pc-card-tips-status0" v-if="currentStatus === systemConfig.liveManagement.waiting"
                            ><span class="dot"></span>{{ lang.waiting }}</span
                        >
                        <span class="pc-card-tips-status2" v-if="currentStatus === systemConfig.liveManagement.starting"
                            ><span class="dot"></span>{{ lang.live_broadcasting }}</span
                        >
                        <span class="pc-card-tips-status3" v-if="currentStatus === systemConfig.liveManagement.end"
                            ><span class="dot"></span>{{ lang.live_broadcast_end }}</span
                        >
                        <span class="pc-card-tips-status3" v-if="currentStatus === systemConfig.liveManagement.cancel"
                            ><span class="dot"></span>{{ lang.live_broadcast_cancel }}</span
                        >
                        <span class="pc-card-tips-status3" v-if="currentStatus === -1"
                            ><span class="dot"></span>{{ lang.conneting }}</span
                        >
                    </div>
                    <div class="pc-status-indicator" v-else>
                        <div class="pc-subject" v-if="livingStatus === 1">
                            <span>{{ `${hostInfo.nickname}${lang.initiated_live_broadcast}` }}</span>
                        </div>
                        <span class="pc-card-tips-status0" v-if="livingStatus === 0"
                            ><span class="dot"></span>{{ lang.not_started }}</span
                        >
                        <span class="pc-card-tips-status2" v-if="livingStatus === 1"
                            ><span class="dot"></span>{{ lang.live_broadcasting }}</span
                        >
                        <span class="pc-card-tips-status3" v-if="livingStatus === 2"
                            ><span class="dot"></span>{{ lang.live_broadcast_end }}</span
                        >
                    </div>
                    <div class="pc-info-item">
                        <div class="pc-info-label">{{lang.user_name}}：</div>
                        <div class="pc-info-value">{{ myNickName }}<van-icon name="edit" @click.native.stop="changeNickName" color='#333' size='20px'/></div>
                    </div>
                    <div class="pc-info-item" v-if="live_id">
                        <div class="pc-info-label">{{ lang.live_theme }}：</div>
                        <div class="pc-info-value">{{ title }}</div>
                    </div>
                    <div class="pc-info-item" v-if="live_id">
                        <div class="pc-info-label">{{ lang.live_des }}：</div>
                        <div class="pc-info-value">{{ dec }}</div>
                    </div>
                    <div class="pc-download-link">
                        <a href="javascript:void(0)" @click="gotoDownLoadClient">{{ lang.weblive_download_client_tips }} >></a>
                    </div>
                </div>
            </div>
        </div>
        <CommonDialog
            v-model="showNewNameDialog"
            :title="lang.input_your_name"
            :showRejectButton="false"
            :showCancelButton="false"
            :forbiddenClose="true"
            :beforeClose="handleCloseNewNameDialog"
        >
            <van-form ref="newNameForm" validate-trigger="onSubmit" label-width="0">
                <van-field
                    v-model.trim="newNameForm.name"
                    :placeholder="`${lang.input_your_name}`"
                    maxlength="100"
                    name="name"
                    :rules="[{ validator: validatorRenameValue, message: `${lang.web_live_name_support_tips}` }]"
                    @blur.stop
                />
            </van-form>
        </CommonDialog>
    </div>
</template>
<script>
import base from "../lib/base";
import service from "../service/service";
import Tool from "@/common/tool.js";
import AgoraRTC from "agora-rtc-sdk-ng";
import customPlayer from "../components/customPlayer.vue";
import vConsole from "vconsole";
import language from "@/common/language";
import { Dialog, Button,Form,Field,Icon  } from "vant";
import CommonDialog from "../components/commonDialog.vue";
import { Header } from "element-ui";
export default {
    mixins: [base],
    name: "WebLive",
    components: {
        customPlayer,
        [Dialog.Component.name]: Dialog.Component,
        VanButton: Button,
        CommonDialog,
        VanForm:Form,
        VanField:Field,
        VanIcon:Icon,
        ElHeader: Header
    },
    data() {
        return {
            formatDateTime: Tool.formatDateTime,
            type: "hls",
            livingStatus: 0, //是否直播中
            main_info: "",
            aux_info: "",
            currentPlayer: "main", //aux
            tmpToken: "",
            currentStatus: -1,
            live_id: 0,
            creator_id: 0,
            title: "",
            dec: "",
            start_ts: "",
            end_ts: "",
            webLiveInterval: null,
            mainInfoInterval: null,
            orientationCheckInterval: null, // 横屏检测定时器
            clientEnv: null, //浏览器标识
            agoraClient: null,
            agoraOptions: {
                appid: "",
                token: "",
                channel: "",
                uid: null,
                uuid:0
            },
            localTracks: {
                videoTrack: null,
                audioTrack: null,
            },
            remoteUsers: {},
            channel_id: 0,
            ajaxServer: "",
            currentSubscribeUidList: [], //从服务器请求的 当前应当订阅的Uidlist
            currentSubscribedMain: { video: false, id: 0 },
            currentSubscribedAux: { video: false, id: 0 },
            currentMute: false,
            isSupport: false,
            currentLayout: ["main", "aux"],
            isFullScreenByManual: false,
            mediaQuery: null,
            isScreenHorizontal: false,
            hostInfo: {},
            showNewNameDialog: false,
            newNameForm:{
                name:''
            },
            myNickName:'',
            oldUid:'',
            joinLiveStamp:0,
            getChannelStatusInterval:null
        };
    },
    computed: {
        serverInfo() {
            return this.systemConfig.serverInfo;
        },
        checkShowSmallPlayerBox() {
            if (this.currentLayout[1] === "aux") {
                if (this.currentSubscribedAux.video) {
                    return true;
                }
            } else {
                if (this.currentSubscribedMain.video) {
                    return true;
                }
            }
            return false;
        },
    },
    beforeCreate() {},
    created() {
        this.getClientEnv();
        // const res = this.checkAgoraSystemRequirements();
        // if (!res) {
        //     return;
        // }
        this.isSupport = true;
        service.query_login_config().then((res) => {
            this.$store.commit("systemConfig/updateSystemConfig", {
                serverInfo: res.data,
            });
        });

        if (window.location.href.includes("mico")) {
            let lang = "EN";
            this.$store.commit("language/setLanguage", language[lang]);
            this.$store.commit("language/setLanguage", { currentLanguage: lang });
        }
    },
    async mounted() {
        document
            .querySelector('meta[name="viewport"]')
            .setAttribute("content", "width=device-width, initial-scale=1,user-scalable=no");
        this.mediaQuery = window.matchMedia("(orientation: portrait)"); // 横屏方向
        this.mediaQuery.addListener(this.handleOrientationChange);

        // 添加window resize监听器，处理iOS设备地址栏隐藏/显示时的高度变化
        if (this.clientEnv.mobile && this.isIOSDevice()) {
            window.addEventListener('resize', this.handleIOSResize);
        }

        // 为移动设备添加额外的横屏检测方式
        if (this.clientEnv.mobile) {
            // 监听window resize事件
            window.addEventListener('resize', this.handleResizeOrientation);
        }

        this.checkOrientation();
        this.setPlayerBoxHeight();

        if (!this.isSupport) {
            return;
        }
        let params = this.$route.params.id;
        let arr = window.atob(params).split("#####");
        arr.forEach((item) => {
            if (item.includes("live_id")) {
                this.live_id = Number(item.split("=")[1]);
            }
            if (item.includes("creator_id")) {
                this.creator_id = Number(item.split("=")[1]);
            }
            if (item.includes("channel_id")) {
                this.agoraOptions.channel = item.split("=")[1];
            }
        });
        if ((!this.live_id || this.live_id === 0) && (!this.agoraOptions.channel || this.agoraOptions.channel == 0)) {
            alert(this.lang.error_live_address_tip);
            return;
        }
        const {isLocal = false,replace = false,agora_ip = ''} = await this.getServerInfo()
        if(isLocal&&!replace){
            // let t = JSON.parse(`{\"log\": {}, \"report\": {} , \"accessPoints\": {\"serverList\": [\"${localAgoraIP}\"], \"domain\": \"${ca_domain}"}}`);
            // this.agoraClient.setLocalAccessPointsV2(t);
            AgoraRTC.setParameter("JOIN_WITH_FALLBACK_SIGNAL_PROXY", false);
            AgoraRTC.setParameter("JOIN_WITH_FALLBACK_MEDIA_PROXY", false);
            AgoraRTC.setParameter('CONNECT_GATEWAY_WITHOUT_DOMAIN',true)
            AgoraRTC.setParameter("WEBCS_DOMAIN",[`${agora_ip}`]);
            AgoraRTC.setParameter("EVENT_REPORT_DOMAIN",`${agora_ip}:6443`);
            AgoraRTC.setParameter("LOG_UPLOAD_SERVER",[`${agora_ip}:6444`]);
            const res = await Tool.checkHttpsConnection(`https://${agora_ip}`)
            if(!res){
                Tool.openCommonDialog({
                    buttons: [this.lang.confirm_txt],
                    message: this.lang.certificate_agree_tips,
                    confirm:()=>{
                        window.open(`https://${agora_ip}`, '_blank');
                    }
                });
                // window.vm.$MessageBox.confirm(lang.certificate_agree_tips, lang.tip_title, {
                //     confirmButtonText: lang.confirm_txt,
                //     callback: async (action) => {
                //         if(action==='confirm'){

                //         }
                //     }
                // });
                this.livingStatus = 2
                return console.error('localAgoraIP disconnect')
            }
        }
        this.$nextTick(()=>{
            this.initNickNameModel()
        })
    },
    beforeDestroy() {
        document.querySelector('meta[name="viewport"]').setAttribute("content", "");
        this.mediaQuery.removeListener(this.handleOrientationChange);

        // 移除window resize监听器
        if (this.clientEnv.mobile && this.isIOSDevice()) {
            window.removeEventListener('resize', this.handleIOSResize);
        }

        // 移除移动设备的额外监听器
        if (this.clientEnv.mobile) {
            window.removeEventListener('resize', this.handleResizeOrientation);
        }
    },
    methods: {
        checkAgoraSystemRequirements() {
            let res = AgoraRTC.checkSystemRequirements();
            this.isSupport = res;
            return res;
        },
        async initAgoraRTC(channelData) {
            //创建本地客户端RTC
            this.agoraOptions.appid = channelData.appid;
            this.agoraOptions.token = channelData.token;
            this.agoraOptions.uid = Number(channelData.uid);
            this.agoraOptions.uuid = channelData.uuid
            this.hostInfo = channelData.hostInfo||{};
            this.agoraClient = AgoraRTC.createClient({ mode: "rtc", codec: "vp8" });
            AgoraRTC.onAutoplayFailed = (e) => {
                console.log("onAutoplayFailed", e);
                Dialog.alert({
                    title: this.lang.tip_title,
                    message: this.lang.recommend_download_app,
                    confirmButtonText: this.lang.confirm_button_text,
                }).then(() => {
                    // 使用统一的音频初始化方法
                    this.initAudioPlayback();

                    this.currentSubscribedMain.id &&
                        this.remoteUsers[this.currentSubscribedMain.id].videoTrack.play("remoteMainPlayer", {
                            fit: "contain",
                        });
                });
            };
            this.agoraClient.on("user-published", this.handleUserPublished);
            this.agoraClient.on("user-unpublished", this.handleUserUnPublished);
            this.agoraClient.on("connection-state-change", this.handleConnectionStateChange);
            this.agoraClient.on("user-joined", this.handleUserJoined);
            this.agoraClient.on("user-left", this.handleUserLeft);
            this.handleChanelStatusChange();
            this.getChannelStatusInterval = setInterval(() => {
                this.handleChanelStatusChange();
            }, 3000);
        },
        async joinRoom() {
            [this.localTracks.audioTrack, this.localTracks.videoTrack] = await Promise.all([
                this.agoraClient.join(
                    this.agoraOptions.appid,
                    this.agoraOptions.channel,
                    this.agoraOptions.token || null,
                    this.agoraOptions.uid
                ),
            ]);
            this.joinLiveStamp = Date.now()
            this.mainInfoInterval = setInterval(() => {
                this.getCurrentSubscribeUidList();
            }, 3000);
            this.getCurrentSubscribeUidList();
            // 初始化音频播放，确保默认开启声音
            setTimeout(() => {
                this.initAudioPlayback();
            }, 1000);
        },
        async handleUserPublished(user, mediaType) {
            const uid = user.uid;
            this.remoteUsers[uid] = user;
            console.log(`User ${uid} published ${mediaType}, currentMute: ${this.currentMute}`);

            if (mediaType === "audio") {
                try {
                    await this.agoraClient.subscribe(user, "audio");
                    if (!this.currentMute) {
                        user.audioTrack.play();
                        console.log(`Auto-playing audio for user ${uid} (not muted)`);
                    } else {
                        console.log(`Audio for user ${uid} not auto-played (muted)`);
                    }
                } catch (error) {
                    console.error(`Error handling audio for user ${uid}:`, error);
                }
            }
        },
        async handleUserUnPublished(user, mediaType) {
            const uid = user.uid;

            if (mediaType === "audio") {
                await this.agoraClient.unsubscribe(user, mediaType);
            } else if (mediaType === "video") {
                if (uid === this.currentSubscribedMain.id) {
                    await this.agoraClient.unsubscribe(user, mediaType);
                    this.currentSubscribedMain[mediaType] = false;
                } else if (uid === this.currentSubscribedAux.id) {
                    await this.agoraClient.unsubscribe(user, mediaType);
                    this.currentSubscribedAux[mediaType] = false;
                }
            }
        },
        async subscribeMain(user) {
            if (user.uid !== this.currentSubscribedMain.id) {
                if (this.currentSubscribedMain.id) {
                    await this.agoraClient.unsubscribe(this.remoteUsers[this.currentSubscribedMain.id]);
                    this.currentSubscribedMain.id = 0;
                }
            }

            try {
                if (!this.currentSubscribedMain.video) {
                    await this.agoraClient.subscribe(user, "video");
                    user.videoTrack.play("remoteMainPlayer", { fit: "contain" });
                    const video = document.querySelector("#remoteMainPlayer video");
                    video.setAttribute("playsinline", "true");
                    video.setAttribute("x5-playsinline", "true");
                    video.setAttribute("webkit-playsinline", "true");
                    this.currentSubscribedMain["video"] = true;
                }
            } catch (error) {
                console.error(error);
            }
            this.currentSubscribedMain.id = user.uid;
        },
        async subscribeAux(user) {
            if (user.uid !== this.currentSubscribedAux.id) {
                if (this.currentSubscribedAux.id) {
                    try {
                        await this.agoraClient.unsubscribe(this.remoteUsers[this.currentSubscribedAux.id], "video");
                    } catch (error) {
                        console.error(error);
                    } finally {
                        this.currentSubscribedAux.id = 0;
                        this.currentSubscribedAux.video = false;
                    }
                }
            }
            try {
                if (!this.currentSubscribedAux.video) {
                    await this.agoraClient.subscribe(user, "video");
                    user.videoTrack.play("remoteAuxPlayer", { fit: "contain" });
                    this.currentSubscribedAux["video"] = true;
                    const video = document.querySelector("#remoteAuxPlayer video");
                    video.setAttribute("playsinline", "true");
                    video.setAttribute("x5-playsinline", "true");
                    video.setAttribute("webkit-playsinline", "true");
                }
            } catch (error) {
                console.error(error, "video");
            }

            this.currentSubscribedAux.id = user.uid;
        },
        // 客户离开信道
        async Leave() {
            this.remoteUsers = {};
            // leave the channel
            this.offAgoraClientEvent();
            this.joinLiveStamp = 0
            await this.agoraClient.leave();
            clearInterval(this.webLiveInterval);
            clearInterval(this.mainInfoInterval);
            clearInterval(this.getChannelStatusInterval);
            this.webLiveInterval = null;
            this.mainInfoInterval = null;
            this.livingStatus = 2;
            this.getChannelStatusInterval = null;
            console.log("客户离开信道成功");
        },
        offAgoraClientEvent() {
            this.agoraClient.off("user-published");
            this.agoraClient.off("user-unpublished");
            this.agoraClient.off("connection-state-change");
            this.agoraClient.off("user-joined");
            this.agoraClient.off("user-left");
        },
        async getWebLiveInfo() {
            let params = {
                videoType: this.type,
                id: this.live_id,
                tmpToken: this.tmpToken,
                creator_id: this.creator_id,
            };
            const { data } = await service.getWebLiveInfo(params);
            console.log("getWebLiveInfo", data);
            if(data.error_code === 0){
                let liveInfo = data.data.liveInfo.extends.livingInfo;
                this.currentStatus = data.data.liveInfo.status;
                this.title = data.data.liveInfo.topic;
                this.start_ts = data.data.liveInfo.start_ts;
                this.end_ts = data.data.liveInfo.end_ts;
                this.dec = data.data.liveInfo.description;
                this.tmpToken = data.data.tmpToken;
                if (this.currentStatus === this.systemConfig.liveManagement.starting) {
                    if (!this.agoraOptions.channel && data.data.channelId) {
                        this.agoraOptions.channel = data.data.channelId;
                        const channelData = await this.getChannelInfo();
                        this.initAgoraRTC(channelData);
                    }
                }
            }
        },
        gotoDownLoadApp() {
            const ajaxServer =
                this.systemConfig.server_type.protocol +
                this.systemConfig.server_type.host +
                this.systemConfig.server_type.port;
            let url = Tool.transferLocationToCe(`${ajaxServer}/activity/activity.html#/qr_install_app`);
            if (url.indexOf("https") < 0) {
                url = url.replace("http", "https");
            }
            url = url.replace("8111", "443");
            window.open(url, "_");
        },
        gotoDownLoadClient() {
            let url = this.serverInfo.installer_url;
            if (url.indexOf("https") < 0) {
                url = url.replace("http", "https");
            }
            url = url.replace("8111", "443");
            window.open(url, "_");
        },
        getClientEnv() {
            let u = navigator.userAgent,
                app = navigator.appVersion;
            this.clientEnv = {
                //移动终端浏览器版本信息
                trident: u.indexOf("Trident") > -1, //IE内核
                presto: u.indexOf("Presto") > -1, //opera内核
                webKit: u.indexOf("AppleWebKit") > -1, //苹果、谷歌内核
                gecko: u.indexOf("Gecko") > -1 && u.indexOf("KHTML") == -1, //火狐内核
                ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
                android: u.indexOf("Android") > -1 || u.indexOf("Linux") > -1, //android终端或者uc浏览器
                iPhone: u.indexOf("iPhone") > -1 || u.indexOf("Mac") > -1, //是否为iPhone或者QQHD浏览器
                iPad: u.indexOf("iPad") > -1, //是否iPad
                webApp: u.indexOf("Safari") == -1, //是否web应该程序，没有头部与底部
                weChat: u.toLowerCase().match(/MicroMessenger/i) == "micromessenger",
                mobile: u.match(
                    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
                ), //是否移动端
                isXiaoMi: u.match(/(xiaoMi|MiuiBrowser)/i),
            };
        },
        // 初始化音频播放 - 确保在默认非静音状态下自动播放音频
        initAudioPlayback() {
            if (!this.currentMute) {
                Object.keys(this.remoteUsers).forEach((uid) => {
                    try {
                        const user = this.remoteUsers[uid];
                        if (user && user.audioTrack) {
                            user.audioTrack.play();
                            console.log(`初始化播放音频 for user ${uid}`);
                        }
                    } catch (error) {
                        console.error(`初始化音频播放失败 for user ${uid}:`, error);
                    }
                });
            }
        },
        toggleMute() {
            this.currentMute = !this.currentMute;
            console.log('toggleMute: currentMute =', this.currentMute);

            Object.keys(this.remoteUsers).forEach((uid) => {
                try {
                    const user = this.remoteUsers[uid];
                    if (user && user.audioTrack) {
                        if (this.currentMute) {
                            user.audioTrack.stop();
                            console.log(`Stopped audio for user ${uid}`);
                        } else {
                            user.audioTrack.play();
                            console.log(`Playing audio for user ${uid}`);
                        }
                    }
                } catch (error) {
                    console.error(`Error controlling audio for user ${uid}:`, error);
                }
            });
        },
        getChannelInfo() {
            return new Promise((resolve, reject) => {
                let params = {
                    channelId: this.agoraOptions.channel,
                    name:this.myNickName
                };
                service.getChannelInfoByVisitor(this.ajaxServer, params).then((res) => {
                    console.log(res, "getChannelInfoByVisitor");
                    if (!res.data.error_code) {
                        resolve(res.data.data);
                    } else {
                        console.error(res.data);
                        // alert('获取token信息失败')
                    }
                });
            });
        },
        getChannelStatus() {
            return new Promise((resolve, reject) => {
                let duration = 0
                if(this.joinLiveStamp){
                    duration = Date.now()-this.joinLiveStamp
                }
                let params = {
                    channelId: this.agoraOptions.channel,
                    nickname:this.myNickName,
                    uuid:this.agoraOptions.uuid,
                    agoraUid:this.agoraOptions.uid,
                    duration,
                };
                service.getChannelStatus(this.ajaxServer, params).then((res) => {
                    console.log(res, "getChannelStatus");
                    if (!res.data.error_code) {
                        resolve(res.data.data);
                    } else {
                        console.error(res.data);
                        // alert('获取token信息失败')
                    }
                });
            });
        },
        getCurrentSubscribeUidList() {
            return new Promise((resolve, reject) => {
                let params = {
                    channelId: this.agoraOptions.channel,
                };
                service.getCurrentSubscribeUidList(this.ajaxServer, params).then((res) => {
                    if (!res.data.error_code) {
                        this.handleSubscribe(res.data.data);
                        this.currentSubscribeUidList = res.data.data;

                        resolve(res.data.data);
                    } else {
                        resolve([]);
                    }
                });
            });
        },
        getServerInfo(){
            return new Promise((resolve,reject)=>{
                service.getServerInfo(this.ajaxServer,{}).then((res) => {
                    if (!res.data.error_code) {
                        resolve(res.data);
                    } else {
                        resolve([]);
                    }
                });
            })
        },
        handleSubscribe(list = []) {
            if (list[0]) {
                if (this.remoteUsers[list[0]]) {
                    this.subscribeMain(this.remoteUsers[list[0]]);
                }
            }
            if (list[1]) {
                if (this.remoteUsers[list[1]]) {
                    this.subscribeAux(this.remoteUsers[list[1]]);
                }
            }
        },
        handleConnectionStateChange(curState, revState, reason) {
            console.log(curState, revState, reason);
            if (curState === "DISCONNECTED") {
                this.Leave();
            }
        },
        swapArrayElements(arr) {
            [arr[0], arr[1]] = [arr[1], arr[0]];
        },
        swapVideoContent() {
            const node1 = document.getElementById("player-box-1");
            const node2 = document.getElementById("player-box-2");
            Tool.swapChildren(node1, node2);
            this.swapArrayElements(this.currentLayout);
            console.log(this.currentLayout);
        },
        toggleFullscreen() {
            this.isFullScreenByManual = !this.isFullScreenByManual;
            if (!this.isScreenHorizontal) {
                if (this.isFullScreenByManual) {
                    // 移动端使用 fullscreenElement，PC端使用 pcFullscreenElement
                    const targetElement = this.clientEnv.mobile ? this.$refs.fullscreenElement : this.$refs.pcFullscreenElement;
                    if (targetElement) {
                        targetElement.style.height = "auto";
                    }
                } else {
                    this.setPlayerBoxHeight();
                }
            } else {
                this.setPlayerBoxHeight();
            }
            this.resetPlayer2Position();
        },
        setPlayerBoxHeight() {
            if (this.clientEnv.mobile) {
                // 在移动设备横屏模式下，使用window.innerHeight获取实际可视高度
                if (this.isScreenHorizontal) {
                    const actualViewportHeight = window.innerHeight;
                    console.log('移动设备横屏模式，实际可视高度:', actualViewportHeight);
                    this.$refs.fullscreenElement && (this.$refs.fullscreenElement.style.height = actualViewportHeight + "px");
                } else {
                    // 竖屏时使用原有逻辑
                    var screenWidth = window.screen.width;
                    var screenHeight = window.screen.height;
                    var minValue = Math.min(screenWidth, screenHeight);
                    this.$refs.fullscreenElement && (this.$refs.fullscreenElement.style.height = minValue + "px");
                }
            } else {
                // PC端不需要设置高度，由CSS控制
            }
        },
        isIOSDevice() {
            // 检测是否为iOS设备，包括iPhone和iPad
            return this.clientEnv.ios || this.clientEnv.iPhone || this.clientEnv.iPad;
        },
        handleIOSResize() {
            // 处理iOS设备窗口大小变化，特别是地址栏隐藏/显示时
            if (this.isScreenHorizontal && this.isFullScreenByManual) {
                console.log('iOS窗口大小变化，重新调整播放器高度');
                this.$nextTick(() => {
                    this.setPlayerBoxHeight();
                });
            }
        },
        handleResizeOrientation() {
            // 使用简单的尺寸比较检测横屏状态
            if (this.clientEnv.mobile && window.screen.width < 768) {
                const isCurrentlyLandscape = window.innerWidth > window.innerHeight;

                if (isCurrentlyLandscape !== this.isScreenHorizontal) {
                    this.updateOrientationState(isCurrentlyLandscape);
                }
            }
        },
        detectLandscapeOrientation() {
            // 使用通用的检测方式
            // 优先使用matchMedia，备用方案是window尺寸比较
            if (window.matchMedia) {
                return window.matchMedia("(orientation: landscape)").matches;
            }

            // 备用方案：简单的尺寸比较
            return window.innerWidth > window.innerHeight;
        },
        updateOrientationState(isLandscape) {
            // 统一的方向状态更新方法
            this.isScreenHorizontal = isLandscape;
            this.isFullScreenByManual = isLandscape;

            this.$nextTick(() => {
                this.setPlayerBoxHeight();
                this.resetPlayer2Position();
            });
        },
        checkOrientation() {
            if (window.screen.width < 768) {
                // 使用统一的检测方法
                const isLandscape = this.detectLandscapeOrientation();

                this.isScreenHorizontal = isLandscape;
                this.isFullScreenByManual = isLandscape;
            }
        },
        resetPlayer2Position() {
            const node2 = document.getElementById("player-box-2");
            if (node2) {
                node2.style.right = 0;
                node2.style.top = 0;
                node2.style.left = "auto";
                node2.style.bottom = "auto";
                console.log(node2.style);
            }
        },
        handleOrientationChange(event) {
            console.log("handleOrientation", event.matches);
            if (window.screen.width < 768) {
                if (!event.matches) {
                    //横屏
                    this.isFullScreenByManual = true;
                    this.isScreenHorizontal = true;
                    // 使用setTimeout确保DOM更新后再设置高度
                    this.$nextTick(() => {
                        this.setPlayerBoxHeight();
                    });
                } else {
                    //竖屏
                    // this.isFullScreenByManual = false
                    this.isScreenHorizontal = false;
                    if (this.isFullScreenByManual) {
                        // 移动端使用 fullscreenElement，PC端使用 pcFullscreenElement
                        const targetElement = this.clientEnv.mobile ? this.$refs.fullscreenElement : this.$refs.pcFullscreenElement;
                        if (targetElement) {
                            targetElement.style.height = "auto";
                        }
                    } else {
                        this.setPlayerBoxHeight();
                    }
                }
                this.resetPlayer2Position();
            }
        },
        async handleChanelStatusChange() {
            const res = await this.getChannelStatus();
            let lastStatus = this.livingStatus;
            if (lastStatus === 1 && !res.status) {
                this.Leave();
            } else if(lastStatus === 0 && res.status) {
                await this.joinRoom()
                // 延迟一下确保用户已经加入并订阅
                setTimeout(() => {
                    this.initAudioPlayback();
                }, 2000);
            }
            this.livingStatus = res.status ? 1 : 0;
        },
        async handleUserJoined(user) {
            const res = await this.getChannelStatus();
            this.livingStatus = res.status ? 1 : 0;
        },
        async handleUserLeft() {},
        refreshStatus() {
            location.reload();
        },
        async handleCloseNewNameDialog(action,done) {
            try {
                await this.$refs.newNameForm.validate()
                done()
                window.localStorage.setItem('nickNameInfo',`${this.newNameForm.name}`)
                if(!this.myNickName){
                    this.myNickName = this.newNameForm.name
                    if (this.agoraOptions.channel) {
                        const channelData = await this.getChannelInfo();
                        this.initAgoraRTC(channelData);
                    } else {
                        this.webLiveInterval = setInterval(() => {
                            this.getWebLiveInfo();
                        }, 2000);
                        this.getWebLiveInfo();
                    }
                    this.main_info = this.lang.switch_video_to_aux;
                    this.aux_info = this.lang.switch_video_to_main;
                }else{
                    this.myNickName = this.newNameForm.name
                }
            } catch (error) {
                done(false)
            }
        },
        validatorRenameValue(val){
            //字母、数字、中文字符、空格、下划线和横杠，并限制总长度为 1 到 100 个字符
            let reg = /^[A-Za-z0-9\u4e00-\u9fa5\s_-]{1,100}$/
            if(reg.test(val)){
                return true
            }
            return false
        },
        initNickNameModel(){
            const nickNameInfo = window.localStorage.getItem('nickNameInfo')
            if(nickNameInfo){
                this.newNameForm.name = nickNameInfo
            }
            this.showNewNameDialog = true
        },
        changeNickName(){
            this.showNewNameDialog = true
        },
        toggleBrowserFullscreen() {
            const playerElement = document.querySelector('.pc-left-container');

            if (!document.fullscreenElement &&
                !document.mozFullScreenElement &&
                !document.webkitFullscreenElement &&
                !document.msFullscreenElement) {
                // 进入全屏
                if (playerElement.requestFullscreen) {
                    playerElement.requestFullscreen();
                } else if (playerElement.msRequestFullscreen) {
                    playerElement.msRequestFullscreen();
                } else if (playerElement.mozRequestFullScreen) {
                    playerElement.mozRequestFullScreen();
                } else if (playerElement.webkitRequestFullscreen) {
                    playerElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
                }
            } else {
                // 退出全屏
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                }
            }
        },
        getStatusClass() {
            if (this.currentStatus === this.systemConfig.liveManagement.waiting) {
                return "status-dot-waiting";
            } else if (this.currentStatus === this.systemConfig.liveManagement.starting) {
                return "status-dot-live";
            } else if (this.currentStatus === this.systemConfig.liveManagement.end) {
                return "status-dot-end";
            } else if (this.currentStatus === this.systemConfig.liveManagement.cancel) {
                return "status-dot-cancel";
            } else if (this.currentStatus === -1) {
                return "status-dot-connecting";
            }
            return "";
        },
        getStatusText() {
            if (this.currentStatus === this.systemConfig.liveManagement.waiting) {
                return this.lang.waiting;
            } else if (this.currentStatus === this.systemConfig.liveManagement.starting) {
                return this.lang.live_broadcasting;
            } else if (this.currentStatus === this.systemConfig.liveManagement.end) {
                return this.lang.live_broadcast_end;
            } else if (this.currentStatus === this.systemConfig.liveManagement.cancel) {
                return this.lang.live_broadcast_cancel;
            } else if (this.currentStatus === -1) {
                return this.lang.conneting;
            }
            return "";
        },
        getTempStatusClass() {
            if (this.livingStatus === 0) {
                return "status-dot-not-started";
            } else if (this.livingStatus === 1) {
                return "status-dot-live";
            } else if (this.livingStatus === 2) {
                return "status-dot-end";
            }
            return "";
        },
        getTempStatusText() {
            if (this.livingStatus === 0) {
                return this.lang.not_started;
            } else if (this.livingStatus === 1) {
                return this.lang.live_broadcasting;
            } else if (this.livingStatus === 2) {
                return this.lang.live_broadcast_end;
            }
            return "";
        }
    },
};
</script>
<style lang="scss">
body,
html {
    height: 100%;
    line-height: 1.4;
    font-size: 20px;
    color: #333;
    background-color: #cfe5e3;
    font-family: NOTOSANSREGULAR;
}
.webLive_header {
    display: flex;
    flex-direction: column !important;
    line-height: unset !important;
    .living_status {
        font-size: 14px;
    }
}
#remoteMainPlayer,
#remoteAuxPlayer {
    flex: 1;
    // height: 150px;
    // max-height: 80vh;
}
.player-container {
    display: flex;
    flex-direction: column;
    overflow: hidden !important;
    height: 20rem;
    position: relative;
    &.fullscreen {
        position: fixed !important;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
    }
    &.horizontal {
        .controls {
            height: 1.1rem;
            i {
                font-size: 0.8rem !important;
            }
        }
        .player-box {
            #player-box-2 {
                width: 4rem;
                height: 4rem;
            }
        }
    }
    .player-box {
        position: relative;
        flex: 1;
        #player-box-1 {
            width: 100%;
            height: 100%;
            background: #0c7bea;
            position: absolute;
            left: 0;
            top: 0;
        }
        #player-box-2 {
            width: 6rem;
            height: 6rem;
            position: absolute;
            right: 0;
            top: 0;
            background: #c4a20a;
        }
    }
    .controls {
        width: 100%;
        height: 2rem;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
        i {
            font-size: 1.5rem !important;
            color: white !important;
        }
        .con_right {
            display: flex;
        }
    }
    .refresh_button {
        position: absolute;
        left: 50%;
        top: 70%;
        transform: translate(-50%, -50%);
    }
}

.player-box-1 {
    // position: absolute;
    // width: 100%;
    // height: 400px;
    position: relative;
}
video {
    display: block;
    width: 100%;
    height: 100%;
    // max-height: 90vh;
}

.primary_bg {
    background-color: #00c59d;
    color: #fff;
}
.title,
.dec {
    padding: 5px 10px;
    font-size: 14px;
    overflow-wrap: anywhere;
}
.title{
    display: flex;
    align-items: center;
}
.status-content {
    display: flex;
    justify-content: flex-end;
    padding: 10px;
    font-size: 12px;
    &.status-content2 {
        justify-content: space-between;
    }
    span {
        text-align: right;
        flex-shrink: 0;
    }
    .date {
        color: #0c7bea;
    }
    .subject {
        color: #0c7bea;
    }
    .card-tips-status0 {
        color: #0c7bea;
    }
    .card-tips-status1 {
        color: #c4a20a;
    }
    .card-tips-status2 {
        color: #3dc40a;
    }
    .card-tips-status3 {
        color: #fb1212;
    }
}
.downLoadTips {
    text-align: center;
    font-size: 16px;
    color: blue;
    text-decoration: underline;
}
.weixin-tip {
    // display: none;
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    filter: alpha(opacity=80);
    height: 100%;
    width: 100%;
    z-index: 100;
    h3 {
        max-width: 100%;
        height: auto;
        text-align: center;
        color: #ffff4f;
    }
    .tip-content {
        text-align: center;
        margin-top: 80%;
        padding: 0 5%;
    }
}

/* PC端样式 */
.pc-layout {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: #f5f7fa;
}

.el-pc-header {
    padding: 0;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    height: 60px !important;
}

.el-pc-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 20px;
}

.el-pc-header-title {
    font-size: 18px;
    font-weight: bold;
    color: #000;
}

.el-pc-header-right {
    display: flex;
    align-items: center;

    .van-button {
        margin-left: 10px;
        cursor: pointer;
    }
}

.pc-container {
    display: flex;
    flex: 1;
    padding: 15px;
    gap: 15px;
    height: calc(100vh - 60px);
    background-color: #cfe5e3;
    overflow: hidden;
    position: relative;
}

.pc-left-container {
    flex: 1;
    height: 100%;
}

.pc-player-container {
    height: 100% !important;
    border-radius: 6px;
    overflow: hidden !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    position: relative;

    &.fullscreen {
        position: fixed !important;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
    }

    .pc-player-box {
        position: relative;
        flex: 1;
        display: flex;

        #player-box-1 {
            width: 100%;
            height: 100%;
            background: #0c7bea;
            position: relative;
            transition: width 0.3s ease;
        }

        &.has-aux-video {
            #player-box-1 {
                width: 70%;
            }
        }

        #player-box-2 {
            width: 30%;
            height: 100%;
            background: #c4a20a;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            overflow: hidden;


            .swap-hint {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.6);
                color: #fff;
                padding: 8px 12px;
                border-radius: 4px;
                display: flex;
                flex-direction: column;
                align-items: center;
                opacity: 0;
                transition: opacity 0.3s ease;

                .van-icon {
                    margin-bottom: 4px;
                }

                span {
                    font-size: 12px;
                    white-space: nowrap;
                }
            }
        }
    }

    .pc-player-controls {
        width: 100%;
        height: 40px;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 15px;



        .pc-controls-right {
            display: flex;
            gap: 15px;

                        .pc-control-btn {
                cursor: pointer;
                padding: 8px;
                border-radius: 6px;
                transition: all 0.2s ease;
                border: 1px solid transparent;

                &:hover {
                    background: rgba(255, 255, 255, 0.1);
                }

                &.pc-mute-btn {
                    &.muted {
                        background: rgba(255, 59, 48, 0.9);
                        border-color: rgba(255, 59, 48, 0.5);
                        box-shadow: 0 0 8px rgba(255, 59, 48, 0.4);
                    }

                    &:not(.muted) {
                        background: rgba(34, 197, 94, 0.8);
                        border-color: rgba(34, 197, 94, 0.3);
                    }
                }
            }
        }
    }

    .refresh_button {
        position: absolute;
        left: 50%;
        top: 70%;
        transform: translate(-50%, -50%);
    }
}


.pc-right-container {
    position: absolute;
    right: -300px;
    top: 15px;
    width: 300px;
    height: calc(100% - 30px);
    transition: right 0.3s ease;
    z-index: 100;

    &:hover,
    .info-sidebar-trigger:hover + & {
        right: 15px;
    }

    &:before {
        content: "≪";
        position: absolute;
        left: -20px;
        top: 50%;
        transform: translateY(-50%);
        width: 20px;
        height: 40px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 4px 0 0 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #333;
        font-size: 16px;
        cursor: pointer;
    }
}

.pc-info-card {
    background: #fff;
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
    background-image: url("../../../../static/resource_pc/images/watermark_0.jpg");
}

.pc-status-content {
    display: flex;
    justify-content: center;
    padding: 10px 0 5px 0;
    font-size: 14px;

    .pc-date {
        color: #0c7bea;
    }
}

.pc-status-indicator {
    display: flex;
    justify-content: center;
    padding: 5px 0 15px 0;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    font-size: 14px;

    .pc-subject {
        color: #0c7bea;
    }

    .pc-card-tips-status0 {
        color: #0c7bea;
    }

    .pc-card-tips-status1 {
        color: #c4a20a;
    }

    .pc-card-tips-status2 {
        color: #3dc40a;
    }

    .pc-card-tips-status3 {
        color: #fb1212;
    }

    .dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
        background-color: currentColor;
    }
}

.pc-info-item {
    display: flex;
    margin-bottom: 15px;
    font-size: 14px;

    .pc-info-label {
        flex-shrink: 0;
        font-weight: bold;
        width: 100px;
    }

    .pc-info-value {
        flex: 1;
        display: flex;
        align-items: center;
    }
}

.pc-download-link {
    margin-top: auto;
    padding: 15px 0;
    text-align: center;
    font-size: 16px;

    a {
        color: #0c7bea;
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }
}

.mobile-container {
    display: block;
}

/* 移动端头部样式 */
.mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    z-index: 100;
    transition: transform 0.3s ease-in-out;

    &.hidden-landscape {
        transform: translateY(-100%);
    }

    .header-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    .header-title {
        font-size: 18px;
        font-weight: bold;
        color: #000;
    }

    .header-actions {
        display: flex;
        align-items: center;
    }

    .fullscreen-btn {
        padding: 5px;
        cursor: pointer;
    }
}

/* 移动端状态卡片样式 */
.ios-status-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    margin: 10px 15px;
    padding: 12px 16px;
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease-in-out;

    &.hidden-landscape {
        transform: translateY(-100%);
    }

    .status-indicator {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #333;
    }

    .status-dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;

        &.status-dot-waiting {
            background-color: #ff9500; /* iOS橙色 */
        }

        &.status-dot-live {
            background-color: #34c759; /* iOS绿色 */
            animation: pulse 2s infinite;
        }

        &.status-dot-end {
            background-color: #ff3b30; /* iOS红色 */
        }

        &.status-dot-cancel {
            background-color: #8e8e93; /* iOS灰色 */
        }

        &.status-dot-connecting {
            background-color: #007aff; /* iOS蓝色 */
            animation: pulse 1s infinite;
        }

        &.status-dot-not-started {
            background-color: #8e8e93; /* iOS灰色 */
        }
    }

    .status-text {
        font-weight: 600;
        color: #1d1d1f;
    }

    .live-time {
        font-size: 12px;
        color: #6d6d70;
        font-weight: 500;
    }

    .live-subject {
        font-size: 14px;
        color: #007aff;
        font-weight: 600;
    }
}

/* 移动端信息卡片样式 */
.ios-info-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    margin: 0 15px 15px 15px;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease-in-out;

    &.hidden-landscape {
        transform: translateY(-100%);
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .info-label {
        font-size: 14px;
        color: #6d6d70;
        font-weight: 500;
        flex-shrink: 0;
        margin-right: 10px;
    }

    .info-value {
        font-size: 14px;
        color: #1d1d1f;
        font-weight: 500;
        display: flex;
        align-items: center;
        text-align: left;
        flex: 1;
        word-break: break-all;
    }
}

/* 移动端视频播放器容器样式 */
.ios-player-container {
    position: relative;
    margin: 0 15px 15px 15px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    height: 280px;
    background: #000;

    &.fullscreen {
        position: fixed !important;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        margin: 0;
        height: 100vh; /* 兼容性fallback */
        height: 100dvh; /* 动态视口高度，更适合移动设备 */
        border-radius: 0;
        z-index: 1000;
    }

    &.horizontal {
        position: fixed !important;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        margin: 0;
        height: 100vh; /* 兼容性fallback */
        height: 100dvh; /* 动态视口高度，更适合移动设备 */
        border-radius: 0;
        z-index: 1000;
    }

    .player-box {
        position: relative;
        flex: 1;

        #player-box-1 {
            width: 100%;
            height: 100%;
            background: #000;
            position: absolute;
            left: 0;
            top: 0;
        }

        #player-box-2 {
            width: 120px;
            height: 160px;
            background: #333;
            position: absolute;
            right: 10px;
            top: 10px;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.3);

            .aux-video {
                position: relative;
                width: 100%;
                height: 100%;
            }

            .swap-indicator {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.7);
                color: #fff;
                padding: 6px;
                border-radius: 20px;
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            &:hover .swap-indicator {
                opacity: 1;
            }
        }
    }

    .ios-controls {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 60px;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
        z-index: 100;

        &.landscape-controls {
            height: 50px;
            padding: 0 15px;

            .control-btn {
                width: 36px;
                height: 36px;
                border-radius: 18px;
            }
        }

        .controls-left {
            display: flex;
            align-items: center;
        }

        .controls-right {
            display: flex;
            align-items: center;
        }

        .control-btn {
            width: 44px;
            height: 44px;
            border-radius: 22px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;

            &:active {
                transform: scale(0.95);
                background: rgba(255, 255, 255, 0.3);
            }


        }

        .mute-btn {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);

            &.muted {
                background: rgba(255, 59, 48, 0.9);
                border-color: rgba(255, 59, 48, 0.5);
                box-shadow: 0 0 10px rgba(255, 59, 48, 0.5);
            }

            &:not(.muted) {
                background: rgba(34, 197, 94, 0.8);
                border-color: rgba(34, 197, 94, 0.3);
            }
        }

        .fullscreen-btn {
            background: rgba(0, 122, 255, 0.8);
            border-color: rgba(0, 122, 255, 0.3);
        }
    }

    .refresh-container {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 100;
    }

    .ios-refresh-btn {
        background-color: #0c7bea;
        color: #fff;
        border-radius: 20px;
        padding: 8px 15px;
        font-size: 14px;
        font-weight: bold;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
}

/* 移动端下载提示样式 */
.ios-download-tip {
    margin: 0 15px 20px 15px;
    transition: transform 0.3s ease-in-out;

    &.hidden-landscape {
        display: none;
    }

    .download-link {
        background: linear-gradient(135deg, #00c59d, #00a085);
        color: #fff;
        padding: 10px 16px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        box-shadow: 0 2px 10px rgba(0, 197, 157, 0.2);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        &:active {
            transform: scale(0.98);
            box-shadow: 0 1px 6px rgba(0, 197, 157, 0.3);
        }

        &:hover:before {
            left: 100%;
        }

        span {
            font-weight: 500;
        }

        .van-icon {
            animation: bounce 2s infinite;
        }
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-3px);
    }
    60% {
        transform: translateY(-2px);
    }
}


/* 移动端整体布局调整 */
.mobile-container {
    padding-top: 70px; /* 为头部留出空间 */
    min-height: 100vh;
    background: linear-gradient(to bottom, #f2f2f7, #e5e5ea);
    padding-bottom: 30px;
    &.landscape-mode {
        padding-top: 0;

        .mobile-header {
            transform: translateY(-100%);
        }

        .ios-status-card,
        .ios-info-card,
        .ios-download-tip {
            display: none;
        }

        .ios-player-container {
            position: fixed !important;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            margin: 0;
            height: 100vh; /* 兼容性fallback */
            height: 100dvh; /* 动态视口高度，更适合移动设备 */
            border-radius: 0;
            z-index: 1000;
        }
    }
}

/* 添加动画效果 */
@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

</style>


