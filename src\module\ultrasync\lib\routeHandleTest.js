/**
 * 路由处理逻辑测试
 * 验证新的handleRouteChange逻辑
 */

import DialogManager from './dialogManager'

class RouteHandleTest {
    constructor() {
        this.testResults = [];
    }

    /**
     * 运行所有测试
     */
    async runTests() {
        console.log('🧪 开始路由处理逻辑测试...');
        
        this.testResults = [];
        
        // 测试无弹窗时的路由处理
        await this.testNoDialogRoute();
        
        // 测试不可关闭弹窗的路由处理
        await this.testNonClosableDialogRoute();
        
        // 测试可关闭弹窗的路由处理
        await this.testClosableDialogRoute();
        
        // 测试多个弹窗的路由处理
        await this.testMultipleDialogsRoute();
        
        // 输出测试结果
        this.printTestResults();
        
        return this.testResults;
    }

    /**
     * 测试无弹窗时的路由处理
     */
    async testNoDialogRoute() {
        console.log('📋 测试无弹窗时的路由处理...');
        
        try {
            // 确保没有弹窗
            DialogManager.closeAllDialogs();
            
            let nextCalled = false;
            let nextResult = null;
            
            const mockNext = (result) => {
                nextCalled = true;
                nextResult = result;
            };
            
            // 模拟路由变化
            DialogManager.handleRouteChange(
                { path: '/test' },
                { path: '/home' },
                mockNext
            );
            
            this.assert(nextCalled === true, 'next函数被调用');
            this.assert(nextResult === undefined, '无弹窗时允许路由通过');
            
        } catch (error) {
            this.fail('无弹窗路由处理测试', error.message);
        }
    }

    /**
     * 测试不可关闭弹窗的路由处理
     */
    async testNonClosableDialogRoute() {
        console.log('📋 测试不可关闭弹窗的路由处理...');
        
        try {
            // 创建不可关闭的弹窗
            const mockDialog = this.createMockDialog();
            const dialogId = DialogManager.register(mockDialog, {
                open: mockDialog.open.bind(mockDialog),
                close: mockDialog.close.bind(mockDialog),
                canCloseOnPopstate: false, // 不允许popstate关闭
                canClose: true
            });
            
            DialogManager.openDialog(dialogId);
            
            let nextCalled = false;
            let nextResult = null;
            
            const mockNext = (result) => {
                nextCalled = true;
                nextResult = result;
            };
            
            // 模拟路由变化
            DialogManager.handleRouteChange(
                { path: '/test' },
                { path: '/home' },
                mockNext
            );
            
            this.assert(nextCalled === true, 'next函数被调用');
            this.assert(nextResult === false, '不可关闭弹窗阻止路由');
            this.assert(DialogManager.isDialogOpen(dialogId), '弹窗仍然打开');
            
            // 清理
            DialogManager.unregister(dialogId);
            
        } catch (error) {
            this.fail('不可关闭弹窗路由处理测试', error.message);
        }
    }

    /**
     * 测试可关闭弹窗的路由处理
     */
    async testClosableDialogRoute() {
        console.log('📋 测试可关闭弹窗的路由处理...');
        
        try {
            // 创建可关闭的弹窗
            const mockDialog = this.createMockDialog();
            const dialogId = DialogManager.register(mockDialog, {
                open: mockDialog.open.bind(mockDialog),
                close: mockDialog.close.bind(mockDialog),
                canCloseOnPopstate: true, // 允许popstate关闭
                canClose: true
            });
            
            DialogManager.openDialog(dialogId);
            
            let nextCalled = false;
            let nextResult = null;
            
            const mockNext = (result) => {
                nextCalled = true;
                nextResult = result;
            };
            
            // 模拟路由变化
            DialogManager.handleRouteChange(
                { path: '/test' },
                { path: '/home' },
                mockNext
            );
            
            this.assert(nextCalled === true, 'next函数被调用');
            this.assert(nextResult === false, '关闭弹窗但阻止路由');
            this.assert(!DialogManager.isDialogOpen(dialogId), '弹窗已被关闭');
            
            // 清理
            DialogManager.unregister(dialogId);
            
        } catch (error) {
            this.fail('可关闭弹窗路由处理测试', error.message);
        }
    }

    /**
     * 测试多个弹窗的路由处理
     */
    async testMultipleDialogsRoute() {
        console.log('📋 测试多个弹窗的路由处理...');
        
        try {
            // 创建两个弹窗
            const mockDialog1 = this.createMockDialog();
            const mockDialog2 = this.createMockDialog();
            
            const dialogId1 = DialogManager.register(mockDialog1, {
                open: mockDialog1.open.bind(mockDialog1),
                close: mockDialog1.close.bind(mockDialog1),
                canCloseOnPopstate: true,
                canClose: true
            });
            
            const dialogId2 = DialogManager.register(mockDialog2, {
                open: mockDialog2.open.bind(mockDialog2),
                close: mockDialog2.close.bind(mockDialog2),
                canCloseOnPopstate: true,
                canClose: true
            });
            
            // 打开两个弹窗
            DialogManager.openDialog(dialogId1);
            DialogManager.openDialog(dialogId2);
            
            let nextCalled = false;
            let nextResult = null;
            
            const mockNext = (result) => {
                nextCalled = true;
                nextResult = result;
            };
            
            // 模拟路由变化
            DialogManager.handleRouteChange(
                { path: '/test' },
                { path: '/home' },
                mockNext
            );
            
            this.assert(nextCalled === true, 'next函数被调用');
            this.assert(nextResult === false, '关闭最后弹窗但阻止路由');
            this.assert(DialogManager.isDialogOpen(dialogId1), '第一个弹窗仍然打开');
            this.assert(!DialogManager.isDialogOpen(dialogId2), '最后一个弹窗被关闭');
            
            // 清理
            DialogManager.unregister(dialogId1);
            DialogManager.unregister(dialogId2);
            
        } catch (error) {
            this.fail('多个弹窗路由处理测试', error.message);
        }
    }

    /**
     * 创建模拟弹窗
     */
    createMockDialog() {
        return {
            isOpen: false,
            open() {
                this.isOpen = true;
            },
            close() {
                this.isOpen = false;
            }
        };
    }

    /**
     * 断言测试
     */
    assert(condition, testName) {
        if (condition) {
            this.pass(testName);
        } else {
            this.fail(testName, '断言失败');
        }
    }

    /**
     * 记录测试通过
     */
    pass(testName) {
        this.testResults.push({
            name: testName,
            status: 'PASS',
            message: '✅ 通过'
        });
    }

    /**
     * 记录测试失败
     */
    fail(testName, message) {
        this.testResults.push({
            name: testName,
            status: 'FAIL',
            message: `❌ 失败: ${message}`
        });
    }

    /**
     * 打印测试结果
     */
    printTestResults() {
        console.log('\n📊 路由处理逻辑测试结果:');
        console.log('='.repeat(50));
        
        let passCount = 0;
        let failCount = 0;
        
        this.testResults.forEach(result => {
            console.log(`${result.message} ${result.name}`);
            if (result.status === 'PASS') {
                passCount++;
            } else {
                failCount++;
            }
        });
        
        console.log('='.repeat(50));
        console.log(`总计: ${this.testResults.length} 个测试`);
        console.log(`通过: ${passCount} 个`);
        console.log(`失败: ${failCount} 个`);
        console.log(`成功率: ${((passCount / this.testResults.length) * 100).toFixed(1)}%`);
        
        if (failCount === 0) {
            console.log('🎉 所有测试通过！新的路由处理逻辑工作正常');
            console.log('💡 逻辑总结：');
            console.log('  - 无弹窗：允许路由通过');
            console.log('  - 不可关闭弹窗：阻止路由，不关闭弹窗');
            console.log('  - 可关闭弹窗：关闭弹窗，阻止路由');
        } else {
            console.log('⚠️  有测试失败，请检查路由处理逻辑');
        }
    }
}

// 导出测试类
export default RouteHandleTest;

// 如果在浏览器环境中，添加到全局对象以便调试
if (typeof window !== 'undefined') {
    window.RouteHandleTest = RouteHandleTest;
}
