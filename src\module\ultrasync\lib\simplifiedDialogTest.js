/**
 * 简化弹窗管理概念测试
 * 验证"注册即打开，注销即关闭"的概念
 */

import DialogManager from './dialogManager'

class SimplifiedDialogTest {
    constructor() {
        this.testResults = [];
    }

    /**
     * 运行所有测试
     */
    async runTests() {
        console.log('🧪 开始简化弹窗管理概念测试...');
        console.log('💡 核心概念：注册 = 打开，注销 = 关闭');
        
        this.testResults = [];
        
        // 测试注册即打开
        await this.testRegisterIsOpen();
        
        // 测试注销即关闭
        await this.testUnregisterIsClose();
        
        // 测试路由处理逻辑
        await this.testRouteHandling();
        
        // 输出测试结果
        this.printTestResults();
        
        return this.testResults;
    }

    /**
     * 测试注册即打开
     */
    async testRegisterIsOpen() {
        console.log('📋 测试注册即打开...');
        
        try {
            // 创建模拟弹窗
            const mockDialog = this.createMockDialog();
            
            // 初始状态
            this.assert(mockDialog.isOpen === false, '初始状态：弹窗未打开');
            this.assert(DialogManager.hasRegisteredDialogs() === false, '初始状态：无注册弹窗');
            
            // 注册弹窗（即打开弹窗）
            const dialogId = DialogManager.register(mockDialog, {
                open: mockDialog.open.bind(mockDialog),
                close: mockDialog.close.bind(mockDialog),
                canCloseOnPopstate: true,
                canClose: true
            });
            
            // 验证注册即打开
            this.assert(typeof dialogId === 'string', '注册返回弹窗ID');
            this.assert(mockDialog.isOpen === true, '注册后弹窗自动打开');
            this.assert(DialogManager.isDialogRegistered(dialogId), '弹窗已注册');
            this.assert(DialogManager.hasRegisteredDialogs(), '系统有注册的弹窗');
            
            // 清理
            DialogManager.unregister(dialogId);
            
        } catch (error) {
            this.fail('注册即打开测试', error.message);
        }
    }

    /**
     * 测试注销即关闭
     */
    async testUnregisterIsClose() {
        console.log('📋 测试注销即关闭...');
        
        try {
            // 创建模拟弹窗
            const mockDialog = this.createMockDialog();
            
            // 注册弹窗
            const dialogId = DialogManager.register(mockDialog, {
                open: mockDialog.open.bind(mockDialog),
                close: mockDialog.close.bind(mockDialog),
                canCloseOnPopstate: true,
                canClose: true
            });
            
            // 验证已打开
            this.assert(mockDialog.isOpen === true, '注册后弹窗已打开');
            this.assert(DialogManager.isDialogRegistered(dialogId), '弹窗已注册');
            
            // 注销弹窗（即关闭弹窗）
            const unregisterResult = DialogManager.unregister(dialogId);
            
            // 验证注销即关闭
            this.assert(unregisterResult === true, '注销操作成功');
            this.assert(mockDialog.isOpen === false, '注销后弹窗自动关闭');
            this.assert(!DialogManager.isDialogRegistered(dialogId), '弹窗已注销');
            this.assert(!DialogManager.hasRegisteredDialogs(), '系统无注册的弹窗');
            
        } catch (error) {
            this.fail('注销即关闭测试', error.message);
        }
    }

    /**
     * 测试路由处理逻辑
     */
    async testRouteHandling() {
        console.log('📋 测试路由处理逻辑...');
        
        try {
            // 测试无弹窗时的路由处理
            let nextCalled = false;
            let nextResult = null;
            
            const mockNext = (result) => {
                nextCalled = true;
                nextResult = result;
            };
            
            // 无弹窗时
            DialogManager.handleRouteChange({ path: '/test' }, { path: '/home' }, mockNext);
            this.assert(nextCalled === true, '无弹窗时调用next');
            this.assert(nextResult === undefined, '无弹窗时允许路由通过');
            
            // 重置
            nextCalled = false;
            nextResult = null;
            
            // 有可关闭弹窗时
            const mockDialog = this.createMockDialog();
            const dialogId = DialogManager.register(mockDialog, {
                open: mockDialog.open.bind(mockDialog),
                close: mockDialog.close.bind(mockDialog),
                canCloseOnPopstate: true,
                canClose: true
            });
            
            DialogManager.handleRouteChange({ path: '/test' }, { path: '/home' }, mockNext);
            this.assert(nextCalled === true, '有弹窗时调用next');
            this.assert(nextResult === false, '有弹窗时阻止路由');
            this.assert(!DialogManager.isDialogRegistered(dialogId), '弹窗被自动注销');
            this.assert(mockDialog.isOpen === false, '弹窗被自动关闭');
            
        } catch (error) {
            this.fail('路由处理逻辑测试', error.message);
        }
    }

    /**
     * 创建模拟弹窗
     */
    createMockDialog() {
        return {
            isOpen: false,
            open() {
                this.isOpen = true;
                console.log('Mock dialog opened');
            },
            close() {
                this.isOpen = false;
                console.log('Mock dialog closed');
            }
        };
    }

    /**
     * 断言测试
     */
    assert(condition, testName) {
        if (condition) {
            this.pass(testName);
        } else {
            this.fail(testName, '断言失败');
        }
    }

    /**
     * 记录测试通过
     */
    pass(testName) {
        this.testResults.push({
            name: testName,
            status: 'PASS',
            message: '✅ 通过'
        });
    }

    /**
     * 记录测试失败
     */
    fail(testName, message) {
        this.testResults.push({
            name: testName,
            status: 'FAIL',
            message: `❌ 失败: ${message}`
        });
    }

    /**
     * 打印测试结果
     */
    printTestResults() {
        console.log('\n📊 简化弹窗管理概念测试结果:');
        console.log('='.repeat(60));
        
        let passCount = 0;
        let failCount = 0;
        
        this.testResults.forEach(result => {
            console.log(`${result.message} ${result.name}`);
            if (result.status === 'PASS') {
                passCount++;
            } else {
                failCount++;
            }
        });
        
        console.log('='.repeat(60));
        console.log(`总计: ${this.testResults.length} 个测试`);
        console.log(`通过: ${passCount} 个`);
        console.log(`失败: ${failCount} 个`);
        console.log(`成功率: ${((passCount / this.testResults.length) * 100).toFixed(1)}%`);
        
        if (failCount === 0) {
            console.log('🎉 所有测试通过！简化的弹窗管理概念工作正常');
            console.log('');
            console.log('💡 概念总结：');
            console.log('  ✨ 注册 = 打开弹窗');
            console.log('  ✨ 注销 = 关闭弹窗');
            console.log('  ✨ 不再有分离的"打开"和"关闭"方法');
            console.log('  ✨ 路由处理自动注销（关闭）弹窗');
            console.log('  ✨ webview返回手势优先处理弹窗');
        } else {
            console.log('⚠️  有测试失败，请检查简化概念的实现');
        }
    }
}

// 导出测试类
export default SimplifiedDialogTest;

// 如果在浏览器环境中，添加到全局对象以便调试
if (typeof window !== 'undefined') {
    window.SimplifiedDialogTest = SimplifiedDialogTest;
}
